#!/usr/bin/env node

/**
 * Test runner script for AI Navigator Backend
 * 
 * This script provides a comprehensive test runner with memory leak detection,
 * coverage reporting, and detailed test results.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  const border = '='.repeat(60);
  log(border, 'cyan');
  log(`  ${message}`, 'cyan');
  log(border, 'cyan');
}

function logSection(message) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(`  ${message}`, 'blue');
  log(`${'-'.repeat(40)}`, 'blue');
}

function checkTestFiles() {
  logSection('Checking Test Files');
  
  const testFiles = [
    'src/features/features.service.spec.ts',
    'src/features/features.controller.spec.ts',
    'src/entities/entities.service.spec.ts',
    'src/users/users.service.spec.ts',
    'src/reviews/reviews.service.spec.ts',
    'src/auth/auth.controller.spec.ts',
  ];

  const existingFiles = [];
  const missingFiles = [];

  testFiles.forEach(file => {
    if (fs.existsSync(file)) {
      existingFiles.push(file);
      log(`✓ ${file}`, 'green');
    } else {
      missingFiles.push(file);
      log(`✗ ${file}`, 'red');
    }
  });

  log(`\nFound ${existingFiles.length} test files, ${missingFiles.length} missing`, 
      missingFiles.length > 0 ? 'yellow' : 'green');

  return { existingFiles, missingFiles };
}

function runJestCommand(command, description) {
  logSection(description);
  
  try {
    log(`Running: ${command}`, 'cyan');
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe',
      maxBuffer: 1024 * 1024 * 10 // 10MB buffer
    });
    
    log(output, 'green');
    return { success: true, output };
  } catch (error) {
    log(`Error: ${error.message}`, 'red');
    if (error.stdout) {
      log('STDOUT:', 'yellow');
      log(error.stdout, 'reset');
    }
    if (error.stderr) {
      log('STDERR:', 'yellow');
      log(error.stderr, 'reset');
    }
    return { success: false, error: error.message };
  }
}

function runUnitTests() {
  const commands = [
    {
      cmd: 'npx jest --testPathPattern=".*\\.spec\\.ts$" --verbose --detectOpenHandles --forceExit',
      desc: 'Running Unit Tests'
    },
    {
      cmd: 'npx jest --testPathPattern=".*\\.spec\\.ts$" --coverage --detectOpenHandles --forceExit',
      desc: 'Running Unit Tests with Coverage'
    }
  ];

  const results = [];
  
  for (const { cmd, desc } of commands) {
    const result = runJestCommand(cmd, desc);
    results.push({ command: cmd, description: desc, ...result });
    
    if (!result.success) {
      log(`Failed to run: ${desc}`, 'red');
      break;
    }
  }

  return results;
}

function runIntegrationTests() {
  const commands = [
    {
      cmd: 'npx jest --testPathPattern=".*\\.controller\\.spec\\.ts$" --verbose --detectOpenHandles --forceExit',
      desc: 'Running Integration Tests'
    }
  ];

  const results = [];
  
  for (const { cmd, desc } of commands) {
    const result = runJestCommand(cmd, desc);
    results.push({ command: cmd, description: desc, ...result });
    
    if (!result.success) {
      log(`Failed to run: ${desc}`, 'red');
      break;
    }
  }

  return results;
}

function runSpecificTests(pattern) {
  logSection(`Running Tests Matching: ${pattern}`);
  
  const command = `npx jest --testPathPattern="${pattern}" --verbose --detectOpenHandles --forceExit`;
  return runJestCommand(command, `Tests matching: ${pattern}`);
}

function generateTestReport(results) {
  logSection('Test Report Summary');
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = totalTests - passedTests;

  log(`Total test suites: ${totalTests}`, 'cyan');
  log(`Passed: ${passedTests}`, passedTests > 0 ? 'green' : 'reset');
  log(`Failed: ${failedTests}`, failedTests > 0 ? 'red' : 'reset');

  if (failedTests > 0) {
    log('\nFailed Tests:', 'red');
    results.filter(r => !r.success).forEach(result => {
      log(`  ✗ ${result.description}`, 'red');
      log(`    Command: ${result.command}`, 'reset');
      log(`    Error: ${result.error}`, 'reset');
    });
  }

  // Check for coverage report
  const coverageDir = path.join(process.cwd(), 'coverage');
  if (fs.existsSync(coverageDir)) {
    log('\nCoverage report generated in ./coverage/', 'green');
    
    const lcovPath = path.join(coverageDir, 'lcov-report', 'index.html');
    if (fs.existsSync(lcovPath)) {
      log(`Open coverage report: file://${lcovPath}`, 'cyan');
    }
  }

  return { totalTests, passedTests, failedTests };
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  logHeader('AI Navigator Backend Test Runner');

  // Check if we're in the right directory
  if (!fs.existsSync('package.json')) {
    log('Error: package.json not found. Please run this script from the project root.', 'red');
    process.exit(1);
  }

  // Check test files
  const { existingFiles, missingFiles } = checkTestFiles();

  if (existingFiles.length === 0) {
    log('No test files found. Please create test files first.', 'red');
    process.exit(1);
  }

  let results = [];

  switch (command) {
    case 'unit':
      results = runUnitTests();
      break;
    
    case 'integration':
      results = runIntegrationTests();
      break;
    
    case 'all':
    default:
      log('Running all tests...', 'cyan');
      results = [
        ...runUnitTests(),
        ...runIntegrationTests()
      ];
      break;
    
    case 'pattern':
      if (!args[1]) {
        log('Error: Please provide a test pattern. Example: npm run test:script pattern "features"', 'red');
        process.exit(1);
      }
      results = [runSpecificTests(args[1])];
      break;
    
    case 'help':
      log('Usage:', 'cyan');
      log('  npm run test:script [command]', 'reset');
      log('', 'reset');
      log('Commands:', 'cyan');
      log('  unit        Run unit tests only', 'reset');
      log('  integration Run integration tests only', 'reset');
      log('  all         Run all tests (default)', 'reset');
      log('  pattern     Run tests matching pattern', 'reset');
      log('  help        Show this help', 'reset');
      process.exit(0);
  }

  // Generate report
  const summary = generateTestReport(results);

  // Exit with appropriate code
  if (summary.failedTests > 0) {
    log('\nTests failed! 🔴', 'red');
    process.exit(1);
  } else {
    log('\nAll tests passed! 🟢', 'green');
    process.exit(0);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  log(`Uncaught Exception: ${error.message}`, 'red');
  console.error(error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log(`Unhandled Rejection at: ${promise}, reason: ${reason}`, 'red');
  process.exit(1);
});

if (require.main === module) {
  main();
}

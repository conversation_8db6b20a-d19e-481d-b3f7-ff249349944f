
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/edge.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.7.0
 * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
 */
Prisma.prismaVersion = {
  client: "6.7.0",
  engine: "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  role: 'role',
  status: 'status',
  technicalLevel: 'technicalLevel',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio',
  socialLinks: 'socialLinks',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLogin: 'lastLogin'
};

exports.Prisma.EntityTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  websiteUrl: 'websiteUrl',
  entityTypeId: 'entityTypeId',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  foundedYear: 'foundedYear',
  status: 'status',
  socialLinks: 'socialLinks',
  submitterId: 'submitterId',
  legacyId: 'legacyId',
  reviewCount: 'reviewCount',
  avgRating: 'avgRating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  scrapedReviewSentimentScore: 'scrapedReviewSentimentScore',
  scrapedReviewCount: 'scrapedReviewCount',
  employeeCountRange: 'employeeCountRange',
  fundingStage: 'fundingStage',
  locationSummary: 'locationSummary',
  refLink: 'refLink',
  affiliateStatus: 'affiliateStatus'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  parentCategoryId: 'parentCategoryId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TagScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityTagScalarFieldEnum = {
  entityId: 'entityId',
  tagId: 'tagId'
};

exports.Prisma.EntityCategoryScalarFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId',
  assignedAt: 'assignedAt'
};

exports.Prisma.ReviewScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  rating: 'rating',
  title: 'title',
  reviewText: 'reviewText',
  status: 'status',
  helpfulnessScore: 'helpfulnessScore',
  moderatorUserId: 'moderatorUserId',
  moderatedAt: 'moderatedAt',
  moderationNotes: 'moderationNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReviewVoteScalarFieldEnum = {
  reviewId: 'reviewId',
  userId: 'userId',
  vote: 'vote',
  createdAt: 'createdAt'
};

exports.Prisma.EntityDetailsToolScalarFieldEnum = {
  entityId: 'entityId',
  programmingLanguages: 'programmingLanguages',
  frameworks: 'frameworks',
  libraries: 'libraries',
  integrations: 'integrations',
  keyFeatures: 'keyFeatures',
  useCases: 'useCases',
  targetAudience: 'targetAudience',
  learningCurve: 'learningCurve',
  deploymentOptions: 'deploymentOptions',
  supportedOs: 'supportedOs',
  mobileSupport: 'mobileSupport',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  trialAvailable: 'trialAvailable',
  demoAvailable: 'demoAvailable',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  hasFreeTier: 'hasFreeTier',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsAgencyScalarFieldEnum = {
  entityId: 'entityId',
  servicesOffered: 'servicesOffered',
  industryFocus: 'industryFocus',
  targetClientSize: 'targetClientSize',
  targetAudience: 'targetAudience',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo'
};

exports.Prisma.EntityDetailsContentCreatorScalarFieldEnum = {
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  focusAreas: 'focusAreas',
  followerCount: 'followerCount',
  exampleContentUrl: 'exampleContentUrl'
};

exports.Prisma.EntityDetailsCommunityScalarFieldEnum = {
  entityId: 'entityId',
  platform: 'platform',
  memberCount: 'memberCount',
  focusTopics: 'focusTopics',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl'
};

exports.Prisma.EntityDetailsNewsletterScalarFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  mainTopics: 'mainTopics',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName',
  subscriberCount: 'subscriberCount'
};

exports.Prisma.EntityDetailsCourseScalarFieldEnum = {
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  skillLevel: 'skillLevel',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl',
  enrollmentCount: 'enrollmentCount',
  certificateAvailable: 'certificateAvailable'
};

exports.Prisma.UserSavedEntityScalarFieldEnum = {
  userId: 'userId',
  entityId: 'entityId',
  createdAt: 'createdAt'
};

exports.Prisma.UserFollowedTagScalarFieldEnum = {
  userId: 'userId',
  tagId: 'tagId',
  createdAt: 'createdAt'
};

exports.Prisma.UserFollowedCategoryScalarFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId',
  createdAt: 'createdAt'
};

exports.Prisma.UserActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  actionType: 'actionType',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId',
  timestamp: 'timestamp',
  details: 'details'
};

exports.Prisma.UserNotificationSettingsScalarFieldEnum = {
  userId: 'userId',
  emailNewsletter: 'emailNewsletter',
  emailNewEntityInFollowedCategory: 'emailNewEntityInFollowedCategory',
  emailNewEntityInFollowedTag: 'emailNewEntityInFollowedTag',
  emailNewReviewOnSavedEntity: 'emailNewReviewOnSavedEntity',
  emailUpdatesOnSavedEntity: 'emailUpdatesOnSavedEntity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BadgeTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  criteriaDetails: 'criteriaDetails',
  scope: 'scope',
  isAutoGranted: 'isAutoGranted',
  isManualGranted: 'isManualGranted',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserBadgeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  grantedByUserId: 'grantedByUserId',
  notes: 'notes'
};

exports.Prisma.EntityBadgeScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  grantedByUserId: 'grantedByUserId',
  notes: 'notes'
};

exports.Prisma.EntityDetailsDatasetScalarFieldEnum = {
  entityId: 'entityId',
  format: 'format',
  sourceUrl: 'sourceUrl',
  license: 'license',
  sizeInBytes: 'sizeInBytes',
  description: 'description',
  accessNotes: 'accessNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsResearchPaperScalarFieldEnum = {
  entityId: 'entityId',
  publicationDate: 'publicationDate',
  doi: 'doi',
  authors: 'authors',
  abstract: 'abstract',
  journalOrConference: 'journalOrConference',
  publicationUrl: 'publicationUrl',
  citationCount: 'citationCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsSoftwareScalarFieldEnum = {
  entityId: 'entityId',
  repositoryUrl: 'repositoryUrl',
  licenseType: 'licenseType',
  programmingLanguages: 'programmingLanguages',
  platformCompatibility: 'platformCompatibility',
  currentVersion: 'currentVersion',
  releaseDate: 'releaseDate',
  hasFreeTier: 'hasFreeTier',
  useCases: 'useCases',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  integrations: 'integrations',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsModelScalarFieldEnum = {
  entityId: 'entityId',
  modelArchitecture: 'modelArchitecture',
  parametersCount: 'parametersCount',
  trainingDataset: 'trainingDataset',
  performanceMetrics: 'performanceMetrics',
  modelUrl: 'modelUrl',
  license: 'license',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsProjectReferenceScalarFieldEnum = {
  entityId: 'entityId',
  projectStatus: 'projectStatus',
  sourceCodeUrl: 'sourceCodeUrl',
  liveDemoUrl: 'liveDemoUrl',
  technologies: 'technologies',
  projectGoals: 'projectGoals',
  contributors: 'contributors',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsServiceProviderScalarFieldEnum = {
  entityId: 'entityId',
  serviceAreas: 'serviceAreas',
  caseStudiesUrl: 'caseStudiesUrl',
  consultationBookingUrl: 'consultationBookingUrl',
  industrySpecializations: 'industrySpecializations',
  companySizeFocus: 'companySizeFocus',
  hourlyRateRange: 'hourlyRateRange',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsInvestorScalarFieldEnum = {
  entityId: 'entityId',
  investmentFocusAreas: 'investmentFocusAreas',
  portfolioUrl: 'portfolioUrl',
  typicalInvestmentSize: 'typicalInvestmentSize',
  investmentStages: 'investmentStages',
  contactEmail: 'contactEmail',
  preferredCommunication: 'preferredCommunication',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsEventScalarFieldEnum = {
  entityId: 'entityId',
  eventType: 'eventType',
  startDate: 'startDate',
  endDate: 'endDate',
  location: 'location',
  registrationUrl: 'registrationUrl',
  speakerList: 'speakerList',
  agendaUrl: 'agendaUrl',
  price: 'price',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsJobScalarFieldEnum = {
  entityId: 'entityId',
  jobTitle: 'jobTitle',
  companyName: 'companyName',
  locationType: 'locationType',
  salaryRange: 'salaryRange',
  applicationUrl: 'applicationUrl',
  jobDescription: 'jobDescription',
  experienceLevel: 'experienceLevel',
  employmentType: 'employmentType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsGrantScalarFieldEnum = {
  entityId: 'entityId',
  grantingInstitution: 'grantingInstitution',
  eligibilityCriteria: 'eligibilityCriteria',
  applicationDeadline: 'applicationDeadline',
  fundingAmount: 'fundingAmount',
  applicationUrl: 'applicationUrl',
  grantFocusArea: 'grantFocusArea',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsBountyScalarFieldEnum = {
  entityId: 'entityId',
  bountyIssuer: 'bountyIssuer',
  rewardAmount: 'rewardAmount',
  requirements: 'requirements',
  submissionDeadline: 'submissionDeadline',
  platformUrl: 'platformUrl',
  difficultyLevel: 'difficultyLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsHardwareScalarFieldEnum = {
  entityId: 'entityId',
  hardwareType: 'hardwareType',
  specifications: 'specifications',
  manufacturer: 'manufacturer',
  releaseDate: 'releaseDate',
  priceRange: 'priceRange',
  datasheetUrl: 'datasheetUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsNewsScalarFieldEnum = {
  entityId: 'entityId',
  publicationDate: 'publicationDate',
  sourceName: 'sourceName',
  articleUrl: 'articleUrl',
  author: 'author',
  summary: 'summary',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsBookScalarFieldEnum = {
  entityId: 'entityId',
  authorNames: 'authorNames',
  isbn: 'isbn',
  publisher: 'publisher',
  publicationYear: 'publicationYear',
  pageCount: 'pageCount',
  summary: 'summary',
  purchaseUrl: 'purchaseUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsPodcastScalarFieldEnum = {
  entityId: 'entityId',
  hostNames: 'hostNames',
  averageEpisodeLength: 'averageEpisodeLength',
  mainTopics: 'mainTopics',
  listenUrl: 'listenUrl',
  frequency: 'frequency',
  primaryLanguage: 'primaryLanguage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsPlatformScalarFieldEnum = {
  entityId: 'entityId',
  platformType: 'platformType',
  keyServices: 'keyServices',
  documentationUrl: 'documentationUrl',
  pricingModel: 'pricingModel',
  slaUrl: 'slaUrl',
  supportedRegions: 'supportedRegions',
  hasFreeTier: 'hasFreeTier',
  useCases: 'useCases',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  integrations: 'integrations',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityFeatureScalarFieldEnum = {
  entityId: 'entityId',
  featureId: 'featureId',
  assignedAt: 'assignedAt'
};

exports.Prisma.FeatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio'
};

exports.Prisma.EntityTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl'
};

exports.Prisma.EntityOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  websiteUrl: 'websiteUrl',
  entityTypeId: 'entityTypeId',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  submitterId: 'submitterId',
  legacyId: 'legacyId',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  employeeCountRange: 'employeeCountRange',
  fundingStage: 'fundingStage',
  locationSummary: 'locationSummary',
  refLink: 'refLink'
};

exports.Prisma.CategoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  parentCategoryId: 'parentCategoryId'
};

exports.Prisma.TagOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl'
};

exports.Prisma.EntityTagOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  tagId: 'tagId'
};

exports.Prisma.EntityCategoryOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId'
};

exports.Prisma.ReviewOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  title: 'title',
  reviewText: 'reviewText',
  moderatorUserId: 'moderatorUserId',
  moderationNotes: 'moderationNotes'
};

exports.Prisma.ReviewVoteOrderByRelevanceFieldEnum = {
  reviewId: 'reviewId',
  userId: 'userId'
};

exports.Prisma.EntityDetailsToolOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  customizationLevel: 'customizationLevel',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsAgencyOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo'
};

exports.Prisma.EntityDetailsContentCreatorOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  exampleContentUrl: 'exampleContentUrl'
};

exports.Prisma.EntityDetailsCommunityOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  platform: 'platform',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl'
};

exports.Prisma.EntityDetailsNewsletterOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName'
};

exports.Prisma.EntityDetailsCourseOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl'
};

exports.Prisma.UserSavedEntityOrderByRelevanceFieldEnum = {
  userId: 'userId',
  entityId: 'entityId'
};

exports.Prisma.UserFollowedTagOrderByRelevanceFieldEnum = {
  userId: 'userId',
  tagId: 'tagId'
};

exports.Prisma.UserFollowedCategoryOrderByRelevanceFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId'
};

exports.Prisma.UserActivityLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId'
};

exports.Prisma.UserNotificationSettingsOrderByRelevanceFieldEnum = {
  userId: 'userId'
};

exports.Prisma.BadgeTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  criteriaDetails: 'criteriaDetails'
};

exports.Prisma.UserBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  grantedByUserId: 'grantedByUserId',
  notes: 'notes'
};

exports.Prisma.EntityBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  grantedByUserId: 'grantedByUserId',
  notes: 'notes'
};

exports.Prisma.EntityDetailsDatasetOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  format: 'format',
  sourceUrl: 'sourceUrl',
  license: 'license',
  description: 'description',
  accessNotes: 'accessNotes'
};

exports.Prisma.EntityDetailsResearchPaperOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  doi: 'doi',
  abstract: 'abstract',
  journalOrConference: 'journalOrConference',
  publicationUrl: 'publicationUrl'
};

exports.Prisma.EntityDetailsSoftwareOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  repositoryUrl: 'repositoryUrl',
  licenseType: 'licenseType',
  currentVersion: 'currentVersion',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsModelOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  modelArchitecture: 'modelArchitecture',
  trainingDataset: 'trainingDataset',
  modelUrl: 'modelUrl',
  license: 'license'
};

exports.Prisma.EntityDetailsProjectReferenceOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  projectStatus: 'projectStatus',
  sourceCodeUrl: 'sourceCodeUrl',
  liveDemoUrl: 'liveDemoUrl',
  projectGoals: 'projectGoals'
};

exports.Prisma.EntityDetailsServiceProviderOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  caseStudiesUrl: 'caseStudiesUrl',
  consultationBookingUrl: 'consultationBookingUrl',
  companySizeFocus: 'companySizeFocus',
  hourlyRateRange: 'hourlyRateRange'
};

exports.Prisma.EntityDetailsInvestorOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  portfolioUrl: 'portfolioUrl',
  typicalInvestmentSize: 'typicalInvestmentSize',
  contactEmail: 'contactEmail',
  preferredCommunication: 'preferredCommunication'
};

exports.Prisma.EntityDetailsEventOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  eventType: 'eventType',
  location: 'location',
  registrationUrl: 'registrationUrl',
  agendaUrl: 'agendaUrl',
  price: 'price'
};

exports.Prisma.EntityDetailsJobOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  jobTitle: 'jobTitle',
  companyName: 'companyName',
  locationType: 'locationType',
  salaryRange: 'salaryRange',
  applicationUrl: 'applicationUrl',
  jobDescription: 'jobDescription',
  experienceLevel: 'experienceLevel',
  employmentType: 'employmentType'
};

exports.Prisma.EntityDetailsGrantOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  grantingInstitution: 'grantingInstitution',
  eligibilityCriteria: 'eligibilityCriteria',
  fundingAmount: 'fundingAmount',
  applicationUrl: 'applicationUrl',
  grantFocusArea: 'grantFocusArea'
};

exports.Prisma.EntityDetailsBountyOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  bountyIssuer: 'bountyIssuer',
  rewardAmount: 'rewardAmount',
  requirements: 'requirements',
  platformUrl: 'platformUrl',
  difficultyLevel: 'difficultyLevel'
};

exports.Prisma.EntityDetailsHardwareOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  hardwareType: 'hardwareType',
  manufacturer: 'manufacturer',
  priceRange: 'priceRange',
  datasheetUrl: 'datasheetUrl'
};

exports.Prisma.EntityDetailsNewsOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  sourceName: 'sourceName',
  articleUrl: 'articleUrl',
  author: 'author',
  summary: 'summary'
};

exports.Prisma.EntityDetailsBookOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  isbn: 'isbn',
  publisher: 'publisher',
  summary: 'summary',
  purchaseUrl: 'purchaseUrl'
};

exports.Prisma.EntityDetailsPodcastOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  averageEpisodeLength: 'averageEpisodeLength',
  listenUrl: 'listenUrl',
  frequency: 'frequency',
  primaryLanguage: 'primaryLanguage'
};

exports.Prisma.EntityDetailsPlatformOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  platformType: 'platformType',
  documentationUrl: 'documentationUrl',
  slaUrl: 'slaUrl',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityFeatureOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  featureId: 'featureId'
};

exports.Prisma.FeatureOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  MODERATOR: 'MODERATOR'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  SUSPENDED: 'SUSPENDED',
  DELETED: 'DELETED'
};

exports.TechnicalLevel = exports.$Enums.TechnicalLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.EntityStatus = exports.$Enums.EntityStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  REJECTED: 'REJECTED',
  NEEDS_REVISION: 'NEEDS_REVISION',
  INACTIVE: 'INACTIVE',
  ARCHIVED: 'ARCHIVED'
};

exports.AffiliateStatus = exports.$Enums.AffiliateStatus = {
  NONE: 'NONE',
  APPLIED: 'APPLIED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.ReviewStatus = exports.$Enums.ReviewStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.LearningCurve = exports.$Enums.LearningCurve = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
};

exports.PricingModel = exports.$Enums.PricingModel = {
  FREE: 'FREE',
  FREEMIUM: 'FREEMIUM',
  SUBSCRIPTION: 'SUBSCRIPTION',
  PAY_PER_USE: 'PAY_PER_USE',
  ONE_TIME_PURCHASE: 'ONE_TIME_PURCHASE',
  CONTACT_SALES: 'CONTACT_SALES',
  OPEN_SOURCE: 'OPEN_SOURCE'
};

exports.PriceRange = exports.$Enums.PriceRange = {
  FREE: 'FREE',
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  ENTERPRISE: 'ENTERPRISE'
};

exports.SkillLevel = exports.$Enums.SkillLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.ActionType = exports.$Enums.ActionType = {
  VIEW_ENTITY: 'VIEW_ENTITY',
  CLICK_ENTITY_LINK: 'CLICK_ENTITY_LINK',
  SAVE_ENTITY: 'SAVE_ENTITY',
  UNSAVE_ENTITY: 'UNSAVE_ENTITY',
  SUBMIT_REVIEW: 'SUBMIT_REVIEW',
  VOTE_REVIEW: 'VOTE_REVIEW',
  FOLLOW_TAG: 'FOLLOW_TAG',
  UNFOLLOW_TAG: 'UNFOLLOW_TAG',
  FOLLOW_CATEGORY: 'FOLLOW_CATEGORY',
  UNFOLLOW_CATEGORY: 'UNFOLLOW_CATEGORY',
  SEARCH: 'SEARCH',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  SIGNUP: 'SIGNUP',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  GRANT_BADGE: 'GRANT_BADGE',
  REVOKE_BADGE: 'REVOKE_BADGE'
};

exports.BadgeScope = exports.$Enums.BadgeScope = {
  USER: 'USER',
  ENTITY: 'ENTITY'
};

exports.Prisma.ModelName = {
  User: 'User',
  EntityType: 'EntityType',
  Entity: 'Entity',
  Category: 'Category',
  Tag: 'Tag',
  EntityTag: 'EntityTag',
  EntityCategory: 'EntityCategory',
  Review: 'Review',
  ReviewVote: 'ReviewVote',
  EntityDetailsTool: 'EntityDetailsTool',
  EntityDetailsAgency: 'EntityDetailsAgency',
  EntityDetailsContentCreator: 'EntityDetailsContentCreator',
  EntityDetailsCommunity: 'EntityDetailsCommunity',
  EntityDetailsNewsletter: 'EntityDetailsNewsletter',
  EntityDetailsCourse: 'EntityDetailsCourse',
  UserSavedEntity: 'UserSavedEntity',
  UserFollowedTag: 'UserFollowedTag',
  UserFollowedCategory: 'UserFollowedCategory',
  UserActivityLog: 'UserActivityLog',
  UserNotificationSettings: 'UserNotificationSettings',
  BadgeType: 'BadgeType',
  UserBadge: 'UserBadge',
  EntityBadge: 'EntityBadge',
  EntityDetailsDataset: 'EntityDetailsDataset',
  EntityDetailsResearchPaper: 'EntityDetailsResearchPaper',
  EntityDetailsSoftware: 'EntityDetailsSoftware',
  EntityDetailsModel: 'EntityDetailsModel',
  EntityDetailsProjectReference: 'EntityDetailsProjectReference',
  EntityDetailsServiceProvider: 'EntityDetailsServiceProvider',
  EntityDetailsInvestor: 'EntityDetailsInvestor',
  EntityDetailsEvent: 'EntityDetailsEvent',
  EntityDetailsJob: 'EntityDetailsJob',
  EntityDetailsGrant: 'EntityDetailsGrant',
  EntityDetailsBounty: 'EntityDetailsBounty',
  EntityDetailsHardware: 'EntityDetailsHardware',
  EntityDetailsNews: 'EntityDetailsNews',
  EntityDetailsBook: 'EntityDetailsBook',
  EntityDetailsPodcast: 'EntityDetailsPodcast',
  EntityDetailsPlatform: 'EntityDetailsPlatform',
  EntityFeature: 'EntityFeature',
  Feature: 'Feature'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "/Users/<USER>/code-server/AI Nav Backend/generated/prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "darwin-arm64",
        "native": true
      },
      {
        "fromEnvVar": null,
        "value": "debian-openssl-3.0.x"
      }
    ],
    "previewFeatures": [
      "fullTextSearchPostgres",
      "multiSchema"
    ],
    "sourceFilePath": "/Users/<USER>/code-server/AI Nav Backend/prisma/schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../.env"
  },
  "relativePath": "../../prisma",
  "clientVersion": "6.7.0",
  "engineVersion": "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\n// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?\n// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init\n\ngenerator client {\n  provider        = \"prisma-client-js\"\n  output          = \"../generated/prisma\"\n  previewFeatures = [\"multiSchema\", \"fullTextSearchPostgres\"]\n  binaryTargets   = [\"native\", \"debian-openssl-3.0.x\"]\n}\n\ndatasource db {\n  provider = \"postgresql\"\n  url      = env(\"DATABASE_URL\")\n  schemas  = [\"public\"]\n}\n\n// Enums based on database inspection\nenum UserRole {\n  USER\n  ADMIN\n  MODERATOR\n  // Add other roles discovered if any\n\n  @@schema(\"public\")\n}\n\nenum UserStatus {\n  ACTIVE\n  INACTIVE\n  PENDING\n  SUSPENDED\n  DELETED // Added for soft deletion\n\n  @@schema(\"public\")\n}\n\nenum TechnicalLevel {\n  BEGINNER\n  INTERMEDIATE\n  ADVANCED\n  EXPERT\n  // Add other levels discovered if any\n\n  @@schema(\"public\")\n}\n\nenum EntityStatus {\n  PENDING\n  ACTIVE\n  REJECTED\n  NEEDS_REVISION\n  INACTIVE\n  ARCHIVED // Added for soft deletion\n\n  @@schema(\"public\")\n}\n\nenum AffiliateStatus {\n  NONE\n  APPLIED\n  APPROVED\n  REJECTED\n  // Add other statuses discovered if any\n\n  @@schema(\"public\")\n}\n\nenum ReviewStatus {\n  PENDING\n  APPROVED\n  REJECTED\n  // Add other statuses discovered if any\n\n  @@schema(\"public\")\n}\n\nenum LearningCurve {\n  LOW\n  MEDIUM\n  HIGH\n  // Add other curves discovered if any\n\n  @@schema(\"public\")\n}\n\nenum PricingModel {\n  FREE\n  FREEMIUM\n  SUBSCRIPTION // Replaces PAID, or is distinct if PAID was meant to be kept and mapped\n  PAY_PER_USE // Replaces USAGE_BASED\n  ONE_TIME_PURCHASE // Replaces ONE_TIME\n  CONTACT_SALES // New\n  OPEN_SOURCE // New\n  // OTHER // Intentionally removed as per plan\n\n  @@schema(\"public\")\n}\n\nenum PriceRange {\n  FREE\n  LOW\n  MEDIUM\n  HIGH\n  ENTERPRISE\n  // Add other ranges discovered if any\n\n  @@schema(\"public\")\n}\n\nenum SkillLevel {\n  BEGINNER\n  INTERMEDIATE\n  ADVANCED\n  EXPERT\n  // Add other levels discovered if any\n\n  @@schema(\"public\")\n}\n\nenum ActionType {\n  VIEW_ENTITY\n  CLICK_ENTITY_LINK\n  SAVE_ENTITY\n  UNSAVE_ENTITY\n  SUBMIT_REVIEW\n  VOTE_REVIEW\n  FOLLOW_TAG\n  UNFOLLOW_TAG\n  FOLLOW_CATEGORY\n  UNFOLLOW_CATEGORY\n  SEARCH\n  LOGIN\n  LOGOUT\n  SIGNUP\n  UPDATE_PROFILE\n  GRANT_BADGE\n  REVOKE_BADGE\n  // Add other actions discovered if any\n\n  @@schema(\"public\")\n}\n\nenum BadgeScope {\n  USER\n  ENTITY\n  // Add other scopes discovered if any\n\n  @@schema(\"public\")\n}\n\n// Models based on database inspection\n\nmodel User {\n  id                       String                    @id @default(uuid()) @db.Uuid\n  authUserId               String                    @unique @map(\"auth_user_id\") @db.Uuid\n  username                 String?                   @unique\n  displayName              String?                   @map(\"display_name\")\n  email                    String                    @unique\n  role                     UserRole                  @default(USER)\n  status                   UserStatus                @default(ACTIVE)\n  technicalLevel           TechnicalLevel?           @map(\"technical_level\")\n  profilePictureUrl        String?                   @map(\"profile_picture_url\")\n  bio                      String?\n  socialLinks              Json?                     @map(\"social_links\") @db.JsonB\n  createdAt                DateTime                  @default(now()) @map(\"created_at\")\n  updatedAt                DateTime                  @updatedAt @map(\"updated_at\")\n  lastLogin                DateTime?                 @map(\"last_login\")\n  reviews                  Review[]\n  reviewsModerated         Review[]                  @relation(\"ModeratorReviews\")\n  userSavedEntities        UserSavedEntity[]         @relation(\"UserSavedEntities\")\n  userFollowedTags         UserFollowedTag[]         @relation(\"UserFollowedTags\")\n  userFollowedCategories   UserFollowedCategory[]    @relation(\"UserFollowedCategories\")\n  submittedEntities        Entity[]                  @relation(\"Submitter\")\n  userActivityLogs         UserActivityLog[]         @relation(\"UserLogs\")\n  userActivityLogTargets   UserActivityLog[]         @relation(\"TargetUserLogs\")\n  userNotificationSettings UserNotificationSettings? @relation(\"UserNotificationSettings\")\n  userBadges               UserBadge[]               @relation(\"UserBadges\")\n  badgesGranted            UserBadge[]               @relation(\"UserBadgesGranted\")\n  entityBadgesGranted      EntityBadge[]             @relation(\"EntityBadgesGranted\")\n  reviewVotes              ReviewVote[]              @relation(\"UserReviewVotes\")\n\n  @@map(\"users\")\n  @@schema(\"public\")\n}\n\nmodel EntityType {\n  id          String   @id @default(uuid()) @db.Uuid\n  name        String   @unique\n  description String?\n  slug        String   @unique\n  iconUrl     String?  @map(\"icon_url\")\n  createdAt   DateTime @default(now()) @map(\"created_at\")\n  updatedAt   DateTime @updatedAt @map(\"updated_at\")\n  entities    Entity[]\n\n  @@map(\"entity_types\")\n  @@schema(\"public\")\n}\n\nmodel Entity {\n  id               String       @id @default(uuid()) @db.Uuid\n  name             String\n  websiteUrl       String?      @map(\"website_url\")\n  entityTypeId     String       @map(\"entity_type_id\") @db.Uuid\n  shortDescription String?      @map(\"short_description\")\n  description      String?\n  logoUrl          String?      @map(\"logo_url\")\n  documentationUrl String?      @map(\"documentation_url\")\n  contactUrl       String?      @map(\"contact_url\")\n  privacyPolicyUrl String?      @map(\"privacy_policy_url\")\n  foundedYear      Int?         @map(\"founded_year\")\n  status           EntityStatus @default(PENDING)\n  socialLinks      Json?        @map(\"social_links\") @db.JsonB\n  submitterId      String       @map(\"submitter_id\") @db.Uuid\n  legacyId         String?      @map(\"legacy_id\")\n  reviewCount      Int          @default(0) @map(\"review_count\")\n  avgRating        Float        @default(0) @map(\"avg_rating\")\n  createdAt        DateTime     @default(now()) @map(\"created_at\")\n  updatedAt        DateTime     @updatedAt @map(\"updated_at\")\n\n  // SEO\n  metaTitle       String? @map(\"meta_title\")\n  metaDescription String? @map(\"meta_description\")\n\n  // Scraped Review Sentiment\n  scrapedReviewSentimentLabel String? @map(\"scraped_review_sentiment_label\")\n  scrapedReviewSentimentScore Float?  @map(\"scraped_review_sentiment_score\")\n  scrapedReviewCount          Int?    @default(0) @map(\"scraped_review_count\")\n\n  // Company Info\n  employeeCountRange String? @map(\"employee_count_range\")\n  fundingStage       String? @map(\"funding_stage\")\n  locationSummary    String? @map(\"location_summary\")\n\n  // Core fields\n  refLink         String?          @map(\"ref_link\")\n  affiliateStatus AffiliateStatus? @default(NONE) // Ensuring type and default\n\n  // Vector Embedding\n  vectorEmbedding Unsupported(\"vector(1536)\")? @map(\"vector_embedding\")\n\n  // Relations\n  entityType        EntityType        @relation(fields: [entityTypeId], references: [id], map: \"fk_entity_entity_type_id\")\n  submitter         User              @relation(\"Submitter\", fields: [submitterId], references: [id], map: \"fk_entity_submitter_id\")\n  entityCategories  EntityCategory[]  @relation(\"EntityToCategories\")\n  entityTags        EntityTag[]       @relation(\"EntityToTags\")\n  reviews           Review[]          @relation(\"EntityReviews\")\n  userSavedEntities UserSavedEntity[] @relation(\"SavedEntities\")\n  userActivityLogs  UserActivityLog[] @relation(\"EntityLogs\")\n  entityBadges      EntityBadge[]     @relation(\"EntityBadges\")\n  entityFeatures    EntityFeature[]   @relation(\"EntityToFeatures\")\n\n  // Entity Detail Relations (One-to-One)\n  entityDetailsTool             EntityDetailsTool?             @relation(\"EntityToDetailsTool\")\n  entityDetailsCourse           EntityDetailsCourse?           @relation(\"EntityToDetailsCourse\")\n  entityDetailsDataset          EntityDetailsDataset?          @relation(\"EntityToDetailsDataset\")\n  entityDetailsResearchPaper    EntityDetailsResearchPaper?    @relation(\"EntityToDetailsResearchPaper\")\n  entityDetailsSoftware         EntityDetailsSoftware?         @relation(\"EntityToDetailsSoftware\")\n  entityDetailsModel            EntityDetailsModel?            @relation(\"EntityToDetailsModel\")\n  entityDetailsProjectReference EntityDetailsProjectReference? @relation(\"EntityToDetailsProjectReference\")\n  entityDetailsServiceProvider  EntityDetailsServiceProvider?  @relation(\"EntityToDetailsServiceProvider\")\n  entityDetailsInvestor         EntityDetailsInvestor?         @relation(\"EntityToDetailsInvestor\")\n  entityDetailsCommunity        EntityDetailsCommunity?        @relation(\"EntityToDetailsCommunity\")\n  entityDetailsEvent            EntityDetailsEvent?            @relation(\"EntityToDetailsEvent\")\n  entityDetailsJob              EntityDetailsJob?              @relation(\"EntityToDetailsJob\")\n  entityDetailsGrant            EntityDetailsGrant?            @relation(\"EntityToDetailsGrant\")\n  entityDetailsBounty           EntityDetailsBounty?           @relation(\"EntityToDetailsBounty\")\n  entityDetailsHardware         EntityDetailsHardware?         @relation(\"EntityToDetailsHardware\")\n  entityDetailsNews             EntityDetailsNews?             @relation(\"EntityToDetailsNews\")\n  entityDetailsBook             EntityDetailsBook?             @relation(\"EntityToDetailsBook\")\n  entityDetailsPodcast          EntityDetailsPodcast?          @relation(\"EntityToDetailsPodcast\")\n  entityDetailsNewsletter       EntityDetailsNewsletter?       @relation(\"EntityToDetailsNewsletter\")\n  entityDetailsPlatform         EntityDetailsPlatform?         @relation(\"EntityToDetailsPlatform\")\n  entityDetailsAgency           EntityDetailsAgency?           @relation(\"EntityToDetailsAgency\")\n  entityDetailsContentCreator   EntityDetailsContentCreator?   @relation(\"EntityToDetailsContentCreator\")\n\n  // For PostgreSQL full-text search on name, description, and shortDescription:\n  // @@fulltext([name, description, shortDescription], map: \"entity_full_text_search_idx\") // Commented out due to P1012\n\n  @@unique([name])\n  @@index([entityTypeId], map: \"idx_entity_entity_type_id\")\n  @@index([submitterId])\n  @@index([status])\n  @@index([createdAt])\n  @@index([updatedAt])\n  @@index([name])\n  @@map(\"entities\")\n  @@schema(\"public\")\n}\n\nmodel Category {\n  id                     String                 @id @default(dbgenerated(\"gen_random_uuid()\")) @db.Uuid\n  name                   String                 @unique\n  slug                   String                 @unique\n  description            String?\n  iconUrl                String?                @map(\"icon_url\")\n  parentCategoryId       String?                @map(\"parent_category_id\") @db.Uuid\n  createdAt              DateTime               @default(now()) @map(\"created_at\")\n  updatedAt              DateTime               @updatedAt @map(\"updated_at\")\n  entities               EntityCategory[]       @relation(\"CategoryToEntities\")\n  parentCategory         Category?              @relation(\"SubCategories\", fields: [parentCategoryId], references: [id], onDelete: SetNull, map: \"fk_category_parent_category_id\")\n  subCategories          Category[]             @relation(\"SubCategories\")\n  userFollowedCategories UserFollowedCategory[] @relation(\"FollowedCategories\")\n  userActivityLogs       UserActivityLog[]      @relation(\"CategoryLogs\")\n\n  @@index([parentCategoryId])\n  @@map(\"categories\")\n  @@schema(\"public\")\n}\n\nmodel Tag {\n  id               String            @id @default(dbgenerated(\"gen_random_uuid()\")) @db.Uuid\n  name             String            @unique\n  description      String?\n  slug             String            @unique\n  iconUrl          String?           @map(\"icon_url\")\n  createdAt        DateTime          @default(now()) @map(\"created_at\")\n  updatedAt        DateTime          @updatedAt @map(\"updated_at\")\n  entities         EntityTag[]       @relation(\"TagToEntities\")\n  userFollowedTags UserFollowedTag[] @relation(\"FollowedTags\")\n  userActivityLogs UserActivityLog[] @relation(\"TagLogs\")\n\n  @@map(\"tags\")\n  @@schema(\"public\")\n}\n\nmodel EntityTag {\n  entityId String @map(\"entity_id\") @db.Uuid\n  tagId    String @map(\"tag_id\") @db.Uuid\n  entity   Entity @relation(\"EntityToTags\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_tag_entity_id\")\n  tag      Tag    @relation(\"TagToEntities\", fields: [tagId], references: [id], onDelete: Cascade, map: \"fk_entity_tag_tag_id\")\n\n  @@id([entityId, tagId])\n  @@map(\"entity_tags\")\n  @@schema(\"public\")\n}\n\nmodel EntityCategory {\n  entityId   String   @map(\"entity_id\") @db.Uuid\n  categoryId String   @map(\"category_id\") @db.Uuid\n  assignedAt DateTime @default(now()) @map(\"assigned_at\")\n\n  entity   Entity   @relation(\"EntityToCategories\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_category_entity_id\")\n  category Category @relation(\"CategoryToEntities\", fields: [categoryId], references: [id], onDelete: Cascade, map: \"fk_entity_category_category_id\")\n\n  @@id([entityId, categoryId], map: \"entity_categories_pkey\")\n  @@index([categoryId], map: \"idx_entity_category_category_id\")\n  @@index([entityId], map: \"idx_entity_category_entity_id\")\n  @@map(\"entity_categories\")\n  @@schema(\"public\")\n}\n\nmodel Review {\n  id               String            @id @default(dbgenerated(\"gen_random_uuid()\")) @db.Uuid\n  entityId         String            @map(\"entity_id\") @db.Uuid\n  userId           String            @map(\"user_id\") @db.Uuid\n  rating           Int\n  title            String?\n  reviewText       String?           @map(\"review_text\")\n  status           ReviewStatus      @default(PENDING)\n  helpfulnessScore Int               @default(0) @map(\"helpfulness_score\")\n  moderatorUserId  String?           @map(\"moderator_user_id\") @db.Uuid\n  moderatedAt      DateTime?         @map(\"moderated_at\")\n  moderationNotes  String?           @map(\"moderation_notes\")\n  createdAt        DateTime          @default(now()) @map(\"created_at\")\n  updatedAt        DateTime          @updatedAt @map(\"updated_at\")\n  entity           Entity            @relation(\"EntityReviews\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_review_entity_id\")\n  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade, map: \"fk_review_user_id\")\n  moderator        User?             @relation(\"ModeratorReviews\", fields: [moderatorUserId], references: [id], onDelete: SetNull, map: \"fk_review_moderator_id\")\n  reviewVotes      ReviewVote[]      @relation(\"ReviewVotes\")\n  userActivityLogs UserActivityLog[] @relation(\"ReviewLogs\")\n\n  @@unique([userId, entityId])\n  @@index([entityId])\n  @@index([userId])\n  @@index([moderatorUserId])\n  @@map(\"reviews\")\n  @@schema(\"public\")\n}\n\nmodel ReviewVote {\n  reviewId  String   @map(\"review_id\") @db.Uuid\n  userId    String   @map(\"user_id\") @db.Uuid\n  vote      Int      @db.SmallInt\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  review    Review   @relation(\"ReviewVotes\", fields: [reviewId], references: [id], onDelete: Cascade)\n  user      User     @relation(\"UserReviewVotes\", fields: [userId], references: [id], onDelete: Cascade)\n\n  @@id([reviewId, userId])\n  @@map(\"review_votes\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsTool {\n  entityId             String         @id @map(\"entity_id\") @db.Uuid\n  programmingLanguages Json?          @map(\"programming_languages\") @db.JsonB // Assuming this might store string[]\n  frameworks           Json?          @db.JsonB // Assuming this might store string[]\n  libraries            Json?          @db.JsonB // Assuming this might store string[]\n  integrations         Json?          @map(\"integrations\") @db.JsonB // Will store string[]\n  keyFeatures          Json?          @map(\"key_features\") @db.JsonB // Assuming this might store string[]\n  useCases             Json?          @map(\"use_cases\") @db.JsonB // Will store string[]\n  targetAudience       Json?          @map(\"target_audience\") @db.JsonB // Assuming this might store string[]\n  learningCurve        LearningCurve? @map(\"learning_curve\")\n  deploymentOptions    Json?          @map(\"deployment_options\") @db.JsonB // Assuming this might store string[]\n  supportedOs          Json?          @map(\"supported_os\") @db.JsonB // Assuming this might store string[]\n  mobileSupport        Boolean?       @map(\"mobile_support\")\n  apiAccess            Boolean?       @map(\"api_access\")\n  customizationLevel   String?        @map(\"customization_level\")\n  trialAvailable       Boolean?       @map(\"trial_available\")\n  demoAvailable        Boolean?       @map(\"demo_available\")\n  openSource           Boolean?       @map(\"open_source\")\n  supportChannels      Json?          @map(\"support_channels\") @db.JsonB // Assuming this might store string[]\n\n  // New fields based on plan\n  hasFreeTier    Boolean       @default(false) @map(\"has_free_tier\")\n  pricingModel   PricingModel? @map(\"pricing_model\")\n  priceRange     PriceRange?   @map(\"price_range\")\n  pricingDetails String?       @map(\"pricing_details\")\n  pricingUrl     String?       @map(\"pricing_url\")\n  supportEmail   String?       @map(\"support_email\")\n  hasLiveChat    Boolean?      @default(false) @map(\"has_live_chat\")\n  communityUrl   String?       @map(\"community_url\")\n\n  // Relation\n  entity Entity @relation(\"EntityToDetailsTool\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_tool_entity_id\")\n\n  @@map(\"entity_details_tool\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsAgency {\n  entityId         String  @id @map(\"entity_id\") @db.Uuid\n  servicesOffered  Json?   @map(\"services_offered\") @db.JsonB\n  industryFocus    Json?   @map(\"industry_focus\") @db.JsonB\n  targetClientSize Json?   @map(\"target_client_size\") @db.JsonB\n  targetAudience   Json?   @map(\"target_audience\") @db.JsonB\n  locationSummary  String? @map(\"location_summary\")\n  portfolioUrl     String? @map(\"portfolio_url\")\n  pricingInfo      String? @map(\"pricing_info\")\n  entity           Entity  @relation(\"EntityToDetailsAgency\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_agency_entity_id\")\n\n  @@map(\"entity_details_agency\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsContentCreator {\n  entityId          String  @id @map(\"entity_id\") @db.Uuid\n  creatorName       String? @map(\"creator_name\")\n  primaryPlatform   String? @map(\"primary_platform\")\n  focusAreas        Json?   @map(\"focus_areas\") @db.JsonB\n  followerCount     Int?    @default(0) @map(\"follower_count\")\n  exampleContentUrl String? @map(\"example_content_url\")\n  entity            Entity  @relation(\"EntityToDetailsContentCreator\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_content_creator_entity_id\")\n\n  @@map(\"entity_details_content_creator\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsCommunity {\n  entityId       String  @id @map(\"entity_id\") @db.Uuid\n  platform       String?\n  memberCount    Int?    @default(0) @map(\"member_count\")\n  focusTopics    Json?   @map(\"focus_topics\") @db.JsonB\n  rulesUrl       String? @map(\"rules_url\")\n  inviteUrl      String? @map(\"invite_url\")\n  mainChannelUrl String? @map(\"main_channel_url\")\n  entity         Entity  @relation(\"EntityToDetailsCommunity\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_community_entity_id\")\n\n  @@map(\"entity_details_community\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsNewsletter {\n  entityId        String  @id @map(\"entity_id\") @db.Uuid\n  frequency       String?\n  mainTopics      Json?   @map(\"main_topics\") @db.JsonB\n  archiveUrl      String? @map(\"archive_url\")\n  subscribeUrl    String? @map(\"subscribe_url\")\n  authorName      String? @map(\"author_name\")\n  subscriberCount Int?    @default(0) @map(\"subscriber_count\")\n  entity          Entity  @relation(\"EntityToDetailsNewsletter\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_newsletter_entity_id\")\n\n  @@map(\"entity_details_newsletter\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsCourse {\n  entityId             String      @id @map(\"entity_id\") @db.Uuid\n  instructorName       String?     @map(\"instructor_name\")\n  durationText         String?     @map(\"duration_text\")\n  skillLevel           SkillLevel? @map(\"skill_level\")\n  prerequisites        String?\n  syllabusUrl          String?     @map(\"syllabus_url\")\n  enrollmentCount      Int?        @default(0) @map(\"enrollment_count\")\n  certificateAvailable Boolean?    @default(false) @map(\"certificate_available\")\n  entity               Entity      @relation(\"EntityToDetailsCourse\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_course_entity_id\")\n\n  @@map(\"entity_details_course\")\n  @@schema(\"public\")\n}\n\nmodel UserSavedEntity {\n  userId    String   @map(\"user_id\") @db.Uuid\n  entityId  String   @map(\"entity_id\") @db.Uuid\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  user      User     @relation(\"UserSavedEntities\", fields: [userId], references: [id], onDelete: Cascade, map: \"fk_user_saved_entity_user_id\")\n  entity    Entity   @relation(\"SavedEntities\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_user_saved_entity_entity_id\")\n\n  @@id([userId, entityId])\n  @@map(\"user_saved_entities\")\n  @@schema(\"public\")\n}\n\nmodel UserFollowedTag {\n  userId    String   @map(\"user_id\") @db.Uuid\n  tagId     String   @map(\"tag_id\") @db.Uuid\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  user      User     @relation(\"UserFollowedTags\", fields: [userId], references: [id], onDelete: Cascade)\n  tag       Tag      @relation(\"FollowedTags\", fields: [tagId], references: [id], onDelete: Cascade)\n\n  @@id([userId, tagId])\n  @@map(\"user_followed_tags\")\n  @@schema(\"public\")\n}\n\nmodel UserFollowedCategory {\n  userId     String   @map(\"user_id\") @db.Uuid\n  categoryId String   @map(\"category_id\") @db.Uuid\n  createdAt  DateTime @default(now()) @map(\"created_at\")\n  user       User     @relation(\"UserFollowedCategories\", fields: [userId], references: [id], onDelete: Cascade)\n  category   Category @relation(\"FollowedCategories\", fields: [categoryId], references: [id], onDelete: Cascade)\n\n  @@id([userId, categoryId])\n  @@map(\"user_followed_categories\")\n  @@schema(\"public\")\n}\n\nmodel UserActivityLog {\n  id           String     @id @default(dbgenerated(\"gen_random_uuid()\")) @db.Uuid\n  userId       String     @map(\"user_id\") @db.Uuid\n  actionType   ActionType @map(\"action_type\")\n  entityId     String?    @map(\"entity_id\") @db.Uuid\n  categoryId   String?    @map(\"category_id\") @db.Uuid\n  tagId        String?    @map(\"tag_id\") @db.Uuid\n  reviewId     String?    @map(\"review_id\") @db.Uuid\n  targetUserId String?    @map(\"target_user_id\") @db.Uuid\n  timestamp    DateTime   @default(now())\n  details      Json?      @db.JsonB\n  user         User       @relation(\"UserLogs\", fields: [userId], references: [id], onDelete: Cascade, map: \"fk_user_activity_log_user_id\")\n  entity       Entity?    @relation(\"EntityLogs\", fields: [entityId], references: [id], onDelete: SetNull, map: \"fk_user_activity_log_entity_id\")\n  category     Category?  @relation(\"CategoryLogs\", fields: [categoryId], references: [id], onDelete: SetNull, map: \"fk_user_activity_log_category_id\")\n  tag          Tag?       @relation(\"TagLogs\", fields: [tagId], references: [id], onDelete: SetNull, map: \"fk_user_activity_log_tag_id\")\n  review       Review?    @relation(\"ReviewLogs\", fields: [reviewId], references: [id], onDelete: SetNull, map: \"fk_user_activity_log_review_id\")\n  targetUser   User?      @relation(\"TargetUserLogs\", fields: [targetUserId], references: [id], onDelete: SetNull, map: \"fk_user_activity_log_target_user_id\")\n\n  @@index([userId])\n  @@index([actionType])\n  @@index([entityId])\n  @@index([categoryId])\n  @@index([tagId])\n  @@index([reviewId])\n  @@index([targetUserId])\n  @@map(\"user_activity_logs\")\n  @@schema(\"public\")\n}\n\nmodel UserNotificationSettings {\n  userId                           String   @id @map(\"user_id\") @db.Uuid\n  emailNewsletter                  Boolean  @default(true) @map(\"email_newsletter\")\n  emailNewEntityInFollowedCategory Boolean  @default(true) @map(\"email_new_entity_in_followed_category\")\n  emailNewEntityInFollowedTag      Boolean  @default(false) @map(\"email_new_entity_in_followed_tag\")\n  emailNewReviewOnSavedEntity      Boolean  @default(true) @map(\"email_new_review_on_saved_entity\")\n  emailUpdatesOnSavedEntity        Boolean  @default(false) @map(\"email_updates_on_saved_entity\")\n  createdAt                        DateTime @default(now()) @map(\"created_at\")\n  updatedAt                        DateTime @updatedAt @map(\"updated_at\")\n  user                             User     @relation(\"UserNotificationSettings\", fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"user_notification_settings\")\n  @@schema(\"public\")\n}\n\nmodel BadgeType {\n  id              String        @id @default(dbgenerated(\"gen_random_uuid()\")) @db.Uuid\n  name            String        @unique\n  slug            String        @unique\n  description     String\n  iconUrl         String        @map(\"icon_url\")\n  criteriaDetails String?       @map(\"criteria_details\")\n  scope           BadgeScope\n  isAutoGranted   Boolean       @default(false) @map(\"is_auto_granted\")\n  isManualGranted Boolean       @default(true) @map(\"is_manual_granted\")\n  createdAt       DateTime      @default(now()) @map(\"created_at\")\n  updatedAt       DateTime      @updatedAt @map(\"updated_at\")\n  userBadges      UserBadge[]   @relation(\"BadgeTypeUser\")\n  entityBadges    EntityBadge[] @relation(\"BadgeTypeEntity\")\n\n  @@map(\"badge_types\")\n  @@schema(\"public\")\n}\n\nmodel UserBadge {\n  id              String    @id @default(dbgenerated(\"gen_random_uuid()\")) @db.Uuid\n  userId          String    @map(\"user_id\") @db.Uuid\n  badgeTypeId     String    @map(\"badge_type_id\") @db.Uuid\n  grantedAt       DateTime  @default(now()) @map(\"granted_at\")\n  grantedByUserId String?   @map(\"granted_by_user_id\") @db.Uuid\n  notes           String?\n  user            User      @relation(\"UserBadges\", fields: [userId], references: [id], onDelete: Cascade)\n  badgeType       BadgeType @relation(\"BadgeTypeUser\", fields: [badgeTypeId], references: [id], onDelete: Cascade)\n  grantedByUser   User?     @relation(\"UserBadgesGranted\", fields: [grantedByUserId], references: [id], onDelete: SetNull)\n\n  @@index([userId])\n  @@index([badgeTypeId])\n  @@index([grantedByUserId])\n  @@map(\"user_badges\")\n  @@schema(\"public\")\n}\n\nmodel EntityBadge {\n  id              String    @id @default(dbgenerated(\"gen_random_uuid()\")) @db.Uuid\n  entityId        String    @map(\"entity_id\") @db.Uuid\n  badgeTypeId     String    @map(\"badge_type_id\") @db.Uuid\n  grantedAt       DateTime  @default(now()) @map(\"granted_at\")\n  grantedByUserId String?   @map(\"granted_by_user_id\") @db.Uuid\n  notes           String?\n  entity          Entity    @relation(\"EntityBadges\", fields: [entityId], references: [id], onDelete: Cascade)\n  badgeType       BadgeType @relation(\"BadgeTypeEntity\", fields: [badgeTypeId], references: [id], onDelete: Cascade)\n  grantedByUser   User?     @relation(\"EntityBadgesGranted\", fields: [grantedByUserId], references: [id], onDelete: SetNull)\n\n  @@index([entityId])\n  @@index([badgeTypeId])\n  @@index([grantedByUserId])\n  @@map(\"entity_badges\")\n  @@schema(\"public\")\n}\n\n// NOTE: We haven't explicitly defined a model for auth.users here.\n// Prisma can reference it via the FK in entities.verifiedBy, but we won't manage auth.users directly via this Prisma schema.\n// Interactions with Supabase Auth should happen via the Supabase client library (e.g., in AuthService).\n\n// Updated definition for EntityDetailsDataset\n// You will need to add ALL other missing EntityDetails models mentioned in the errors,\n// such as EntityDetailsResearchPaper, EntityDetailsSoftware, etc.\n// Ensure each has a similar one-to-one relation back to the Entity model.\nmodel EntityDetailsDataset {\n  entityId    String  @id @map(\"entity_id\") @db.Uuid\n  format      String? // e.g., CSV, JSON, Parquet\n  sourceUrl   String? @map(\"source_url\")\n  license     String? // e.g., MIT, CC BY 4.0\n  sizeInBytes BigInt? @map(\"size_in_bytes\")\n  description String?\n  accessNotes String? @map(\"access_notes\") // Notes on how to access or use the dataset\n\n  entity    Entity   @relation(\"EntityToDetailsDataset\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_dataset_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_dataset\")\n  @@schema(\"public\")\n}\n\n// Example for EntityDetailsResearchPaper\nmodel EntityDetailsResearchPaper {\n  entityId            String    @id @map(\"entity_id\") @db.Uuid\n  publicationDate     DateTime? @map(\"publication_date\") @db.Date\n  doi                 String?   @unique\n  authors             Json? // Store as array of strings like [\"Author One\", \"Author Two\"]\n  abstract            String?\n  journalOrConference String?   @map(\"journal_or_conference\")\n  publicationUrl      String?   @map(\"publication_url\")\n  citationCount       Int?      @default(0) @map(\"citation_count\")\n\n  entity    Entity   @relation(\"EntityToDetailsResearchPaper\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_research_paper_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_research_paper\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsSoftware {\n  entityId              String    @id @map(\"entity_id\") @db.Uuid\n  repositoryUrl         String?   @map(\"repository_url\")\n  licenseType           String?   @map(\"license_type\")\n  programmingLanguages  Json?     @map(\"programming_languages\") // Array of strings, e.g., [\"Python\", \"JavaScript\"]\n  platformCompatibility Json?     @map(\"platform_compatibility\") // Array of strings, e.g., [\"Windows\", \"Linux\", \"Web\"]\n  currentVersion        String?   @map(\"current_version\")\n  releaseDate           DateTime? @map(\"release_date\") @db.Date\n\n  // Shared fields from EntityDetailsTool\n  hasFreeTier    Boolean       @default(false) @map(\"has_free_tier\")\n  useCases       Json?         @map(\"use_cases\") // Will store string[]\n  pricingModel   PricingModel? @map(\"pricing_model\")\n  priceRange     PriceRange?   @map(\"price_range\")\n  pricingDetails String?       @map(\"pricing_details\")\n  pricingUrl     String?       @map(\"pricing_url\")\n  integrations   Json?         @map(\"integrations\") // Will store string[]\n  supportEmail   String?       @map(\"support_email\")\n  hasLiveChat    Boolean?      @default(false) @map(\"has_live_chat\")\n  communityUrl   String?       @map(\"community_url\")\n\n  entity    Entity   @relation(\"EntityToDetailsSoftware\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_software_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_software\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsModel {\n  entityId           String  @id @map(\"entity_id\") @db.Uuid\n  modelArchitecture  String? @map(\"model_architecture\")\n  parametersCount    BigInt? @map(\"parameters_count\")\n  trainingDataset    String? @map(\"training_dataset\") // Could be a description or link\n  performanceMetrics Json? // e.g., { \"accuracy\": 0.95, \"f1_score\": 0.92 }\n  modelUrl           String? @map(\"model_url\") // Link to download or access the model\n  license            String?\n\n  entity    Entity   @relation(\"EntityToDetailsModel\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_model_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_model\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsProjectReference {\n  entityId      String  @id @map(\"entity_id\") @db.Uuid\n  projectStatus String? @map(\"project_status\") // e.g., active, completed, archived, proof-of-concept\n  sourceCodeUrl String? @map(\"source_code_url\")\n  liveDemoUrl   String? @map(\"live_demo_url\")\n  technologies  Json? // Array of strings, e.g., [\"React\", \"Node.js\", \"PostgreSQL\"]\n  projectGoals  String? @map(\"project_goals\")\n  contributors  Json? // Array of contributor names or objects\n\n  entity    Entity   @relation(\"EntityToDetailsProjectReference\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_project_reference_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_project_reference\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsServiceProvider {\n  entityId                String  @id @map(\"entity_id\") @db.Uuid\n  serviceAreas            Json? // e.g., [\"AI Development\", \"Data Science Consulting\", \"MLOps\"]\n  caseStudiesUrl          String? @map(\"case_studies_url\")\n  consultationBookingUrl  String? @map(\"consultation_booking_url\")\n  industrySpecializations Json? // Array of strings, e.g., [\"Healthcare\", \"Finance\"]\n  companySizeFocus        String? @map(\"company_size_focus\") // e.g., Startups, SMEs, Enterprise\n  hourlyRateRange         String? @map(\"hourly_rate_range\") // e.g., \"$100-$200\"\n\n  entity    Entity   @relation(\"EntityToDetailsServiceProvider\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_service_provider_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_service_provider\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsInvestor {\n  entityId               String  @id @map(\"entity_id\") @db.Uuid\n  investmentFocusAreas   Json? // e.g., [\"Seed Stage AI\", \"Healthcare Tech\", \"SaaS\"]\n  portfolioUrl           String? @map(\"portfolio_url\")\n  typicalInvestmentSize  String? @map(\"typical_investment_size\") // e.g., \"$100k - $1M\"\n  investmentStages       Json? // Array of strings, e.g., [\"Pre-seed\", \"Seed\", \"Series A\"]\n  contactEmail           String? @map(\"contact_email\")\n  preferredCommunication String? @map(\"preferred_communication\")\n\n  entity    Entity   @relation(\"EntityToDetailsInvestor\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_investor_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_investor\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsEvent {\n  entityId        String    @id @map(\"entity_id\") @db.Uuid\n  eventType       String? // e.g., Conference, Webinar, Workshop, Meetup\n  startDate       DateTime? @map(\"start_date\")\n  endDate         DateTime? @map(\"end_date\")\n  location        String? // Can be physical address or \"Online\"\n  registrationUrl String?   @map(\"registration_url\")\n  speakerList     Json? // Array of speaker names or objects\n  agendaUrl       String?   @map(\"agenda_url\")\n  price           String? // e.g., \"Free\", \"$99\", \"Contact for enterprise\"\n\n  entity    Entity   @relation(\"EntityToDetailsEvent\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_event_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_event\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsJob {\n  entityId        String  @id @map(\"entity_id\") @db.Uuid\n  jobTitle        String? @map(\"job_title\")\n  companyName     String? @map(\"company_name\")\n  locationType    String? @map(\"location_type\") // e.g., Remote, On-site, Hybrid\n  salaryRange     String? @map(\"salary_range\") // e.g., \"$80k - $120k\", \"Competitive\"\n  applicationUrl  String? @map(\"application_url\")\n  jobDescription  String? @map(\"job_description\")\n  experienceLevel String? @map(\"experience_level\") // e.g., Entry, Mid, Senior\n  employmentType  String? @map(\"employment_type\") // e.g., Full-time, Part-time, Contract\n\n  entity    Entity   @relation(\"EntityToDetailsJob\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_job_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_job\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsGrant {\n  entityId            String    @id @map(\"entity_id\") @db.Uuid\n  grantingInstitution String?   @map(\"granting_institution\")\n  eligibilityCriteria String?   @map(\"eligibility_criteria\")\n  applicationDeadline DateTime? @map(\"application_deadline\") @db.Date\n  fundingAmount       String?   @map(\"funding_amount\") // e.g., \"$10,000 - $50,000\"\n  applicationUrl      String?   @map(\"application_url\")\n  grantFocusArea      String?   @map(\"grant_focus_area\") // e.g., \"AI Ethics Research\", \"Open Source Development\"\n\n  entity    Entity   @relation(\"EntityToDetailsGrant\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_grant_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_grant\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsBounty {\n  entityId           String    @id @map(\"entity_id\") @db.Uuid\n  bountyIssuer       String?   @map(\"bounty_issuer\")\n  rewardAmount       String?   @map(\"reward_amount\") // e.g., \"1000 USD\", \"0.5 ETH\"\n  requirements       String?\n  submissionDeadline DateTime? @map(\"submission_deadline\") @db.Date\n  platformUrl        String?   @map(\"platform_url\") // e.g., Link to Gitcoin, HackerOne, or custom platform\n  difficultyLevel    String?   @map(\"difficulty_level\") // e.g., Easy, Medium, Hard\n\n  entity    Entity   @relation(\"EntityToDetailsBounty\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_bounty_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_bounty\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsHardware {\n  entityId       String    @id @map(\"entity_id\") @db.Uuid\n  hardwareType   String?   @map(\"hardware_type\") // e.g., GPU, FPGA, ASIC, TPU, AI Accelerator\n  specifications Json? // e.g., { \"memory\": \"24GB GDDR6X\", \"cuda_cores\": 10496, \"tflops\": 35.6 }\n  manufacturer   String?\n  releaseDate    DateTime? @map(\"release_date\") @db.Date\n  priceRange     String?   @map(\"price_range\") // e.g., \"$500 - $1000\"\n  datasheetUrl   String?   @map(\"datasheet_url\")\n\n  entity    Entity   @relation(\"EntityToDetailsHardware\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_hardware_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_hardware\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsNews {\n  entityId        String    @id @map(\"entity_id\") @db.Uuid\n  publicationDate DateTime? @map(\"publication_date\") @db.Date\n  sourceName      String?   @map(\"source_name\")\n  articleUrl      String    @unique @map(\"article_url\")\n  author          String?\n  summary         String? // Brief summary of the news article\n\n  entity    Entity   @relation(\"EntityToDetailsNews\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_news_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_news\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsBook {\n  entityId        String  @id @map(\"entity_id\") @db.Uuid\n  authorNames     Json? // Array of strings, e.g., [\"Author A\", \"Author B\"]\n  isbn            String? @unique\n  publisher       String?\n  publicationYear Int?    @map(\"publication_year\")\n  pageCount       Int?    @map(\"page_count\")\n  summary         String?\n  purchaseUrl     String? @map(\"purchase_url\")\n\n  entity    Entity   @relation(\"EntityToDetailsBook\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_book_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_book\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsPodcast {\n  entityId             String  @id @map(\"entity_id\") @db.Uuid\n  hostNames            Json? // Array of strings\n  averageEpisodeLength String? @map(\"average_episode_length\") // e.g., \"45 minutes\"\n  mainTopics           Json? // Array of strings, e.g., [\"AI Ethics\", \"ML Research\"]\n  listenUrl            String? @map(\"listen_url\") // Link to Spotify, Apple Podcasts, etc.\n  frequency            String? // e.g., \"Weekly\", \"Bi-weekly\"\n  primaryLanguage      String? @default(\"English\") @map(\"primary_language\")\n\n  entity    Entity   @relation(\"EntityToDetailsPodcast\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_podcast_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_podcast\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsPlatform {\n  entityId         String        @id @map(\"entity_id\") @db.Uuid\n  platformType     String?       @map(\"platform_type\") // e.g., PaaS, SaaS, IaaS, MLOps Platform, Data Platform\n  keyServices      Json?         @map(\"key_services\") // Array of strings, e.g., [\"Model Training\", \"Data Annotation\", \"Deployment\"] // Added map\n  documentationUrl String?       @map(\"documentation_url\")\n  pricingModel     PricingModel? @map(\"pricing_model\") // Changed from String? to PricingModel? (Final Change)\n  slaUrl           String?       @map(\"sla_url\")\n  supportedRegions Json?         @map(\"supported_regions\") // Array of strings, e.g., [\"us-east-1\", \"eu-west-2\"]\n\n  // Shared fields from EntityDetailsTool\n  hasFreeTier    Boolean?    @default(false) @map(\"has_free_tier\") // Optional as not in direct SQL\n  useCases       Json?       @map(\"use_cases\") // Will store string[]\n  priceRange     PriceRange? @map(\"price_range\")\n  pricingDetails String?     @map(\"pricing_details\")\n  pricingUrl     String?     @map(\"pricing_url\")\n  integrations   Json?       @map(\"integrations\") // Will store string[]\n  supportEmail   String?     @map(\"support_email\")\n  hasLiveChat    Boolean?    @default(false) @map(\"has_live_chat\")\n  communityUrl   String?     @map(\"community_url\")\n\n  entity    Entity   @relation(\"EntityToDetailsPlatform\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_details_platform_entity_id\")\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  updatedAt DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"entity_details_platform\")\n  @@schema(\"public\")\n}\n\nmodel EntityFeature {\n  entityId   String   @map(\"entity_id\") @db.Uuid\n  featureId  String   @map(\"feature_id\") @db.Uuid\n  assignedAt DateTime @default(now()) @map(\"assigned_at\")\n  // assignedBy could be a String? relation to User id if needed in future\n  // assignedById String? @map(\"assigned_by_id\") @db.Uuid \n  // assignedBy User? @relation(\"FeaturesAssignedByUser\", fields: [assignedById], references: [id])\n\n  entity  Entity  @relation(\"EntityToFeatures\", fields: [entityId], references: [id], onDelete: Cascade, map: \"fk_entity_feature_entity_id\")\n  feature Feature @relation(\"FeatureToEntities\", fields: [featureId], references: [id], onDelete: Cascade, map: \"fk_entity_feature_feature_id\")\n\n  @@id([entityId, featureId], map: \"pk_entity_feature\")\n  @@index([featureId], map: \"idx_entity_feature_feature_id\")\n  @@index([entityId], map: \"idx_entity_feature_entity_id\")\n  @@map(\"entity_features\")\n  @@schema(\"public\")\n}\n\n// Define Feature model here\nmodel Feature {\n  id             String          @id @default(dbgenerated(\"gen_random_uuid()\")) @db.Uuid\n  name           String          @unique\n  slug           String          @unique\n  description    String?\n  iconUrl        String?         @map(\"icon_url\")\n  createdAt      DateTime        @default(now()) @map(\"created_at\")\n  updatedAt      DateTime        @updatedAt @map(\"updated_at\")\n  entityFeatures EntityFeature[] @relation(\"FeatureToEntities\")\n\n  @@map(\"features\")\n  @@schema(\"public\")\n}\n\n// ADD OTHER MISSING EntityDetails... MODELS HERE, following the pattern above.\n// For example:\n// model EntityDetailsSoftware {\n",
  "inlineSchemaHash": "8142308e925ed8a07de86d1a68f87391dc2b3f895e81678f1c461ae793c5517b",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"dbName\":\"users\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"authUserId\",\"dbName\":\"auth_user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"username\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"displayName\",\"dbName\":\"display_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"role\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"UserRole\",\"nativeType\":null,\"default\":\"USER\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"UserStatus\",\"nativeType\":null,\"default\":\"ACTIVE\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"technicalLevel\",\"dbName\":\"technical_level\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TechnicalLevel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"profilePictureUrl\",\"dbName\":\"profile_picture_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"bio\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"socialLinks\",\"dbName\":\"social_links\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"lastLogin\",\"dbName\":\"last_login\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviews\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"ReviewToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewsModerated\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"ModeratorReviews\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userSavedEntities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserSavedEntity\",\"nativeType\":null,\"relationName\":\"UserSavedEntities\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userFollowedTags\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserFollowedTag\",\"nativeType\":null,\"relationName\":\"UserFollowedTags\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userFollowedCategories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserFollowedCategory\",\"nativeType\":null,\"relationName\":\"UserFollowedCategories\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submittedEntities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"Submitter\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"UserLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogTargets\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"TargetUserLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userNotificationSettings\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserNotificationSettings\",\"nativeType\":null,\"relationName\":\"UserNotificationSettings\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userBadges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserBadge\",\"nativeType\":null,\"relationName\":\"UserBadges\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgesGranted\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserBadge\",\"nativeType\":null,\"relationName\":\"UserBadgesGranted\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityBadgesGranted\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityBadge\",\"nativeType\":null,\"relationName\":\"EntityBadgesGranted\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewVotes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReviewVote\",\"nativeType\":null,\"relationName\":\"UserReviewVotes\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityType\":{\"dbName\":\"entity_types\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"entities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityType\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Entity\":{\"dbName\":\"entities\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"websiteUrl\",\"dbName\":\"website_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityTypeId\",\"dbName\":\"entity_type_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shortDescription\",\"dbName\":\"short_description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"logoUrl\",\"dbName\":\"logo_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"documentationUrl\",\"dbName\":\"documentation_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"contactUrl\",\"dbName\":\"contact_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"privacyPolicyUrl\",\"dbName\":\"privacy_policy_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"foundedYear\",\"dbName\":\"founded_year\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"EntityStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"socialLinks\",\"dbName\":\"social_links\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submitterId\",\"dbName\":\"submitter_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"legacyId\",\"dbName\":\"legacy_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewCount\",\"dbName\":\"review_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"avgRating\",\"dbName\":\"avg_rating\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Float\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"metaTitle\",\"dbName\":\"meta_title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"metaDescription\",\"dbName\":\"meta_description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scrapedReviewSentimentLabel\",\"dbName\":\"scraped_review_sentiment_label\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scrapedReviewSentimentScore\",\"dbName\":\"scraped_review_sentiment_score\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scrapedReviewCount\",\"dbName\":\"scraped_review_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"employeeCountRange\",\"dbName\":\"employee_count_range\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fundingStage\",\"dbName\":\"funding_stage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"locationSummary\",\"dbName\":\"location_summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"refLink\",\"dbName\":\"ref_link\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"affiliateStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"AffiliateStatus\",\"nativeType\":null,\"default\":\"NONE\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityType\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityType\",\"nativeType\":null,\"relationName\":\"EntityToEntityType\",\"relationFromFields\":[\"entityTypeId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submitter\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"Submitter\",\"relationFromFields\":[\"submitterId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityCategories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityCategory\",\"nativeType\":null,\"relationName\":\"EntityToCategories\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityTags\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityTag\",\"nativeType\":null,\"relationName\":\"EntityToTags\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviews\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"EntityReviews\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userSavedEntities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserSavedEntity\",\"nativeType\":null,\"relationName\":\"SavedEntities\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"EntityLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityBadges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityBadge\",\"nativeType\":null,\"relationName\":\"EntityBadges\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityFeatures\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityFeature\",\"nativeType\":null,\"relationName\":\"EntityToFeatures\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsTool\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsTool\",\"nativeType\":null,\"relationName\":\"EntityToDetailsTool\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsCourse\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsCourse\",\"nativeType\":null,\"relationName\":\"EntityToDetailsCourse\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsDataset\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsDataset\",\"nativeType\":null,\"relationName\":\"EntityToDetailsDataset\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsResearchPaper\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsResearchPaper\",\"nativeType\":null,\"relationName\":\"EntityToDetailsResearchPaper\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsSoftware\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsSoftware\",\"nativeType\":null,\"relationName\":\"EntityToDetailsSoftware\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsModel\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsModel\",\"nativeType\":null,\"relationName\":\"EntityToDetailsModel\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsProjectReference\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsProjectReference\",\"nativeType\":null,\"relationName\":\"EntityToDetailsProjectReference\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsServiceProvider\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsServiceProvider\",\"nativeType\":null,\"relationName\":\"EntityToDetailsServiceProvider\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsInvestor\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsInvestor\",\"nativeType\":null,\"relationName\":\"EntityToDetailsInvestor\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsCommunity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsCommunity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsCommunity\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsEvent\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsEvent\",\"nativeType\":null,\"relationName\":\"EntityToDetailsEvent\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsJob\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsJob\",\"nativeType\":null,\"relationName\":\"EntityToDetailsJob\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsGrant\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsGrant\",\"nativeType\":null,\"relationName\":\"EntityToDetailsGrant\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsBounty\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsBounty\",\"nativeType\":null,\"relationName\":\"EntityToDetailsBounty\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsHardware\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsHardware\",\"nativeType\":null,\"relationName\":\"EntityToDetailsHardware\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsNews\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsNews\",\"nativeType\":null,\"relationName\":\"EntityToDetailsNews\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsBook\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsBook\",\"nativeType\":null,\"relationName\":\"EntityToDetailsBook\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsPodcast\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsPodcast\",\"nativeType\":null,\"relationName\":\"EntityToDetailsPodcast\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsNewsletter\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsNewsletter\",\"nativeType\":null,\"relationName\":\"EntityToDetailsNewsletter\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsPlatform\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsPlatform\",\"nativeType\":null,\"relationName\":\"EntityToDetailsPlatform\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsAgency\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsAgency\",\"nativeType\":null,\"relationName\":\"EntityToDetailsAgency\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsContentCreator\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsContentCreator\",\"nativeType\":null,\"relationName\":\"EntityToDetailsContentCreator\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"name\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"name\"]}],\"isGenerated\":false},\"Category\":{\"dbName\":\"categories\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"dbgenerated\",\"args\":[\"gen_random_uuid()\"]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"parentCategoryId\",\"dbName\":\"parent_category_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"entities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityCategory\",\"nativeType\":null,\"relationName\":\"CategoryToEntities\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"parentCategory\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"SubCategories\",\"relationFromFields\":[\"parentCategoryId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"subCategories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"SubCategories\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userFollowedCategories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserFollowedCategory\",\"nativeType\":null,\"relationName\":\"FollowedCategories\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"CategoryLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Tag\":{\"dbName\":\"tags\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"dbgenerated\",\"args\":[\"gen_random_uuid()\"]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"entities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityTag\",\"nativeType\":null,\"relationName\":\"TagToEntities\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userFollowedTags\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserFollowedTag\",\"nativeType\":null,\"relationName\":\"FollowedTags\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"TagLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityTag\":{\"dbName\":\"entity_tags\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tagId\",\"dbName\":\"tag_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToTags\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tag\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Tag\",\"nativeType\":null,\"relationName\":\"TagToEntities\",\"relationFromFields\":[\"tagId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"entityId\",\"tagId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityCategory\":{\"dbName\":\"entity_categories\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categoryId\",\"dbName\":\"category_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedAt\",\"dbName\":\"assigned_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToCategories\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"category\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"CategoryToEntities\",\"relationFromFields\":[\"categoryId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"entityId\",\"categoryId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Review\":{\"dbName\":\"reviews\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"dbgenerated\",\"args\":[\"gen_random_uuid()\"]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"rating\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewText\",\"dbName\":\"review_text\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ReviewStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"helpfulnessScore\",\"dbName\":\"helpfulness_score\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"moderatorUserId\",\"dbName\":\"moderator_user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"moderatedAt\",\"dbName\":\"moderated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"moderationNotes\",\"dbName\":\"moderation_notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityReviews\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ReviewToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"moderator\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ModeratorReviews\",\"relationFromFields\":[\"moderatorUserId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewVotes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReviewVote\",\"nativeType\":null,\"relationName\":\"ReviewVotes\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"ReviewLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"userId\",\"entityId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"userId\",\"entityId\"]}],\"isGenerated\":false},\"ReviewVote\":{\"dbName\":\"review_votes\",\"schema\":\"public\",\"fields\":[{\"name\":\"reviewId\",\"dbName\":\"review_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vote\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":[\"SmallInt\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"review\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"ReviewVotes\",\"relationFromFields\":[\"reviewId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserReviewVotes\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"reviewId\",\"userId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsTool\":{\"dbName\":\"entity_details_tool\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"programmingLanguages\",\"dbName\":\"programming_languages\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"frameworks\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"libraries\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"integrations\",\"dbName\":\"integrations\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keyFeatures\",\"dbName\":\"key_features\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"learningCurve\",\"dbName\":\"learning_curve\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"LearningCurve\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deploymentOptions\",\"dbName\":\"deployment_options\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportedOs\",\"dbName\":\"supported_os\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mobileSupport\",\"dbName\":\"mobile_support\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"apiAccess\",\"dbName\":\"api_access\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"customizationLevel\",\"dbName\":\"customization_level\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"trialAvailable\",\"dbName\":\"trial_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"demoAvailable\",\"dbName\":\"demo_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"openSource\",\"dbName\":\"open_source\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportChannels\",\"dbName\":\"support_channels\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasFreeTier\",\"dbName\":\"has_free_tier\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingModel\",\"dbName\":\"pricing_model\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PricingModel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priceRange\",\"dbName\":\"price_range\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PriceRange\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingDetails\",\"dbName\":\"pricing_details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingUrl\",\"dbName\":\"pricing_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportEmail\",\"dbName\":\"support_email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasLiveChat\",\"dbName\":\"has_live_chat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"communityUrl\",\"dbName\":\"community_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsTool\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsAgency\":{\"dbName\":\"entity_details_agency\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"servicesOffered\",\"dbName\":\"services_offered\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"industryFocus\",\"dbName\":\"industry_focus\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetClientSize\",\"dbName\":\"target_client_size\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"locationSummary\",\"dbName\":\"location_summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"portfolioUrl\",\"dbName\":\"portfolio_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingInfo\",\"dbName\":\"pricing_info\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsAgency\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsContentCreator\":{\"dbName\":\"entity_details_content_creator\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"creatorName\",\"dbName\":\"creator_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"primaryPlatform\",\"dbName\":\"primary_platform\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"focusAreas\",\"dbName\":\"focus_areas\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"followerCount\",\"dbName\":\"follower_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"exampleContentUrl\",\"dbName\":\"example_content_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsContentCreator\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsCommunity\":{\"dbName\":\"entity_details_community\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platform\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"memberCount\",\"dbName\":\"member_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"focusTopics\",\"dbName\":\"focus_topics\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"rulesUrl\",\"dbName\":\"rules_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inviteUrl\",\"dbName\":\"invite_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mainChannelUrl\",\"dbName\":\"main_channel_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsCommunity\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsNewsletter\":{\"dbName\":\"entity_details_newsletter\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"frequency\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mainTopics\",\"dbName\":\"main_topics\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"archiveUrl\",\"dbName\":\"archive_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"subscribeUrl\",\"dbName\":\"subscribe_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"authorName\",\"dbName\":\"author_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"subscriberCount\",\"dbName\":\"subscriber_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsNewsletter\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsCourse\":{\"dbName\":\"entity_details_course\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"instructorName\",\"dbName\":\"instructor_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"durationText\",\"dbName\":\"duration_text\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"skillLevel\",\"dbName\":\"skill_level\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"SkillLevel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"prerequisites\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"syllabusUrl\",\"dbName\":\"syllabus_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"enrollmentCount\",\"dbName\":\"enrollment_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"certificateAvailable\",\"dbName\":\"certificate_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsCourse\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserSavedEntity\":{\"dbName\":\"user_saved_entities\",\"schema\":\"public\",\"fields\":[{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserSavedEntities\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"SavedEntities\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"userId\",\"entityId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserFollowedTag\":{\"dbName\":\"user_followed_tags\",\"schema\":\"public\",\"fields\":[{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tagId\",\"dbName\":\"tag_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserFollowedTags\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tag\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Tag\",\"nativeType\":null,\"relationName\":\"FollowedTags\",\"relationFromFields\":[\"tagId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"userId\",\"tagId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserFollowedCategory\":{\"dbName\":\"user_followed_categories\",\"schema\":\"public\",\"fields\":[{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categoryId\",\"dbName\":\"category_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserFollowedCategories\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"category\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"FollowedCategories\",\"relationFromFields\":[\"categoryId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"userId\",\"categoryId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserActivityLog\":{\"dbName\":\"user_activity_logs\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"dbgenerated\",\"args\":[\"gen_random_uuid()\"]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"actionType\",\"dbName\":\"action_type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ActionType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categoryId\",\"dbName\":\"category_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tagId\",\"dbName\":\"tag_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewId\",\"dbName\":\"review_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetUserId\",\"dbName\":\"target_user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"timestamp\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":[\"JsonB\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserLogs\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityLogs\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"category\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"CategoryLogs\",\"relationFromFields\":[\"categoryId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tag\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Tag\",\"nativeType\":null,\"relationName\":\"TagLogs\",\"relationFromFields\":[\"tagId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"review\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"ReviewLogs\",\"relationFromFields\":[\"reviewId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"TargetUserLogs\",\"relationFromFields\":[\"targetUserId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserNotificationSettings\":{\"dbName\":\"user_notification_settings\",\"schema\":\"public\",\"fields\":[{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailNewsletter\",\"dbName\":\"email_newsletter\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailNewEntityInFollowedCategory\",\"dbName\":\"email_new_entity_in_followed_category\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailNewEntityInFollowedTag\",\"dbName\":\"email_new_entity_in_followed_tag\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailNewReviewOnSavedEntity\",\"dbName\":\"email_new_review_on_saved_entity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailUpdatesOnSavedEntity\",\"dbName\":\"email_updates_on_saved_entity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserNotificationSettings\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"BadgeType\":{\"dbName\":\"badge_types\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"dbgenerated\",\"args\":[\"gen_random_uuid()\"]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"criteriaDetails\",\"dbName\":\"criteria_details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scope\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BadgeScope\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isAutoGranted\",\"dbName\":\"is_auto_granted\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isManualGranted\",\"dbName\":\"is_manual_granted\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"userBadges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserBadge\",\"nativeType\":null,\"relationName\":\"BadgeTypeUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityBadges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityBadge\",\"nativeType\":null,\"relationName\":\"BadgeTypeEntity\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserBadge\":{\"dbName\":\"user_badges\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"dbgenerated\",\"args\":[\"gen_random_uuid()\"]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgeTypeId\",\"dbName\":\"badge_type_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedAt\",\"dbName\":\"granted_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedByUserId\",\"dbName\":\"granted_by_user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserBadges\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgeType\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BadgeType\",\"nativeType\":null,\"relationName\":\"BadgeTypeUser\",\"relationFromFields\":[\"badgeTypeId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedByUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserBadgesGranted\",\"relationFromFields\":[\"grantedByUserId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityBadge\":{\"dbName\":\"entity_badges\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"dbgenerated\",\"args\":[\"gen_random_uuid()\"]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgeTypeId\",\"dbName\":\"badge_type_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedAt\",\"dbName\":\"granted_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedByUserId\",\"dbName\":\"granted_by_user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityBadges\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgeType\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BadgeType\",\"nativeType\":null,\"relationName\":\"BadgeTypeEntity\",\"relationFromFields\":[\"badgeTypeId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedByUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"EntityBadgesGranted\",\"relationFromFields\":[\"grantedByUserId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsDataset\":{\"dbName\":\"entity_details_dataset\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"format\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sourceUrl\",\"dbName\":\"source_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"license\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sizeInBytes\",\"dbName\":\"size_in_bytes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BigInt\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"accessNotes\",\"dbName\":\"access_notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsDataset\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsResearchPaper\":{\"dbName\":\"entity_details_research_paper\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publicationDate\",\"dbName\":\"publication_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"Date\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"doi\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"authors\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"abstract\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"journalOrConference\",\"dbName\":\"journal_or_conference\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publicationUrl\",\"dbName\":\"publication_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"citationCount\",\"dbName\":\"citation_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsResearchPaper\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsSoftware\":{\"dbName\":\"entity_details_software\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"repositoryUrl\",\"dbName\":\"repository_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"licenseType\",\"dbName\":\"license_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"programmingLanguages\",\"dbName\":\"programming_languages\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platformCompatibility\",\"dbName\":\"platform_compatibility\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currentVersion\",\"dbName\":\"current_version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"releaseDate\",\"dbName\":\"release_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"Date\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasFreeTier\",\"dbName\":\"has_free_tier\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingModel\",\"dbName\":\"pricing_model\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PricingModel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priceRange\",\"dbName\":\"price_range\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PriceRange\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingDetails\",\"dbName\":\"pricing_details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingUrl\",\"dbName\":\"pricing_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"integrations\",\"dbName\":\"integrations\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportEmail\",\"dbName\":\"support_email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasLiveChat\",\"dbName\":\"has_live_chat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"communityUrl\",\"dbName\":\"community_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsSoftware\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsModel\":{\"dbName\":\"entity_details_model\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"modelArchitecture\",\"dbName\":\"model_architecture\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"parametersCount\",\"dbName\":\"parameters_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BigInt\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"trainingDataset\",\"dbName\":\"training_dataset\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"performanceMetrics\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"modelUrl\",\"dbName\":\"model_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"license\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsModel\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsProjectReference\":{\"dbName\":\"entity_details_project_reference\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"projectStatus\",\"dbName\":\"project_status\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sourceCodeUrl\",\"dbName\":\"source_code_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"liveDemoUrl\",\"dbName\":\"live_demo_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"technologies\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"projectGoals\",\"dbName\":\"project_goals\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"contributors\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsProjectReference\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsServiceProvider\":{\"dbName\":\"entity_details_service_provider\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceAreas\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"caseStudiesUrl\",\"dbName\":\"case_studies_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"consultationBookingUrl\",\"dbName\":\"consultation_booking_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"industrySpecializations\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"companySizeFocus\",\"dbName\":\"company_size_focus\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hourlyRateRange\",\"dbName\":\"hourly_rate_range\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsServiceProvider\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsInvestor\":{\"dbName\":\"entity_details_investor\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"investmentFocusAreas\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"portfolioUrl\",\"dbName\":\"portfolio_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"typicalInvestmentSize\",\"dbName\":\"typical_investment_size\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"investmentStages\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"contactEmail\",\"dbName\":\"contact_email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"preferredCommunication\",\"dbName\":\"preferred_communication\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsInvestor\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsEvent\":{\"dbName\":\"entity_details_event\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"eventType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"startDate\",\"dbName\":\"start_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"endDate\",\"dbName\":\"end_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"location\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"registrationUrl\",\"dbName\":\"registration_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"speakerList\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"agendaUrl\",\"dbName\":\"agenda_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"price\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsEvent\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsJob\":{\"dbName\":\"entity_details_job\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"jobTitle\",\"dbName\":\"job_title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"companyName\",\"dbName\":\"company_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"locationType\",\"dbName\":\"location_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"salaryRange\",\"dbName\":\"salary_range\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"applicationUrl\",\"dbName\":\"application_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"jobDescription\",\"dbName\":\"job_description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"experienceLevel\",\"dbName\":\"experience_level\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"employmentType\",\"dbName\":\"employment_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsJob\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsGrant\":{\"dbName\":\"entity_details_grant\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantingInstitution\",\"dbName\":\"granting_institution\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"eligibilityCriteria\",\"dbName\":\"eligibility_criteria\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"applicationDeadline\",\"dbName\":\"application_deadline\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"Date\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fundingAmount\",\"dbName\":\"funding_amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"applicationUrl\",\"dbName\":\"application_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantFocusArea\",\"dbName\":\"grant_focus_area\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsGrant\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsBounty\":{\"dbName\":\"entity_details_bounty\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"bountyIssuer\",\"dbName\":\"bounty_issuer\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"rewardAmount\",\"dbName\":\"reward_amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"requirements\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submissionDeadline\",\"dbName\":\"submission_deadline\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"Date\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platformUrl\",\"dbName\":\"platform_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"difficultyLevel\",\"dbName\":\"difficulty_level\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsBounty\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsHardware\":{\"dbName\":\"entity_details_hardware\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hardwareType\",\"dbName\":\"hardware_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"specifications\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"manufacturer\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"releaseDate\",\"dbName\":\"release_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"Date\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priceRange\",\"dbName\":\"price_range\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"datasheetUrl\",\"dbName\":\"datasheet_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsHardware\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsNews\":{\"dbName\":\"entity_details_news\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publicationDate\",\"dbName\":\"publication_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"Date\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sourceName\",\"dbName\":\"source_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"articleUrl\",\"dbName\":\"article_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"author\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsNews\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsBook\":{\"dbName\":\"entity_details_book\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"authorNames\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isbn\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publisher\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publicationYear\",\"dbName\":\"publication_year\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pageCount\",\"dbName\":\"page_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"purchaseUrl\",\"dbName\":\"purchase_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsBook\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsPodcast\":{\"dbName\":\"entity_details_podcast\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hostNames\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"averageEpisodeLength\",\"dbName\":\"average_episode_length\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mainTopics\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"listenUrl\",\"dbName\":\"listen_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"frequency\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"primaryLanguage\",\"dbName\":\"primary_language\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":\"English\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsPodcast\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsPlatform\":{\"dbName\":\"entity_details_platform\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platformType\",\"dbName\":\"platform_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keyServices\",\"dbName\":\"key_services\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"documentationUrl\",\"dbName\":\"documentation_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingModel\",\"dbName\":\"pricing_model\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PricingModel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slaUrl\",\"dbName\":\"sla_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportedRegions\",\"dbName\":\"supported_regions\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasFreeTier\",\"dbName\":\"has_free_tier\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priceRange\",\"dbName\":\"price_range\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PriceRange\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingDetails\",\"dbName\":\"pricing_details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingUrl\",\"dbName\":\"pricing_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"integrations\",\"dbName\":\"integrations\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportEmail\",\"dbName\":\"support_email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasLiveChat\",\"dbName\":\"has_live_chat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"communityUrl\",\"dbName\":\"community_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToDetailsPlatform\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityFeature\":{\"dbName\":\"entity_features\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"featureId\",\"dbName\":\"feature_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedAt\",\"dbName\":\"assigned_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToFeatures\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"feature\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Feature\",\"nativeType\":null,\"relationName\":\"FeatureToEntities\",\"relationFromFields\":[\"featureId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"entityId\",\"featureId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Feature\":{\"dbName\":\"features\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"dbgenerated\",\"args\":[\"gen_random_uuid()\"]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"entityFeatures\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityFeature\",\"nativeType\":null,\"relationName\":\"FeatureToEntities\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{\"UserRole\":{\"values\":[{\"name\":\"USER\",\"dbName\":null},{\"name\":\"ADMIN\",\"dbName\":null},{\"name\":\"MODERATOR\",\"dbName\":null}],\"dbName\":null},\"UserStatus\":{\"values\":[{\"name\":\"ACTIVE\",\"dbName\":null},{\"name\":\"INACTIVE\",\"dbName\":null},{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"SUSPENDED\",\"dbName\":null},{\"name\":\"DELETED\",\"dbName\":null}],\"dbName\":null},\"TechnicalLevel\":{\"values\":[{\"name\":\"BEGINNER\",\"dbName\":null},{\"name\":\"INTERMEDIATE\",\"dbName\":null},{\"name\":\"ADVANCED\",\"dbName\":null},{\"name\":\"EXPERT\",\"dbName\":null}],\"dbName\":null},\"EntityStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"ACTIVE\",\"dbName\":null},{\"name\":\"REJECTED\",\"dbName\":null},{\"name\":\"NEEDS_REVISION\",\"dbName\":null},{\"name\":\"INACTIVE\",\"dbName\":null},{\"name\":\"ARCHIVED\",\"dbName\":null}],\"dbName\":null},\"AffiliateStatus\":{\"values\":[{\"name\":\"NONE\",\"dbName\":null},{\"name\":\"APPLIED\",\"dbName\":null},{\"name\":\"APPROVED\",\"dbName\":null},{\"name\":\"REJECTED\",\"dbName\":null}],\"dbName\":null},\"ReviewStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"APPROVED\",\"dbName\":null},{\"name\":\"REJECTED\",\"dbName\":null}],\"dbName\":null},\"LearningCurve\":{\"values\":[{\"name\":\"LOW\",\"dbName\":null},{\"name\":\"MEDIUM\",\"dbName\":null},{\"name\":\"HIGH\",\"dbName\":null}],\"dbName\":null},\"PricingModel\":{\"values\":[{\"name\":\"FREE\",\"dbName\":null},{\"name\":\"FREEMIUM\",\"dbName\":null},{\"name\":\"SUBSCRIPTION\",\"dbName\":null},{\"name\":\"PAY_PER_USE\",\"dbName\":null},{\"name\":\"ONE_TIME_PURCHASE\",\"dbName\":null},{\"name\":\"CONTACT_SALES\",\"dbName\":null},{\"name\":\"OPEN_SOURCE\",\"dbName\":null}],\"dbName\":null},\"PriceRange\":{\"values\":[{\"name\":\"FREE\",\"dbName\":null},{\"name\":\"LOW\",\"dbName\":null},{\"name\":\"MEDIUM\",\"dbName\":null},{\"name\":\"HIGH\",\"dbName\":null},{\"name\":\"ENTERPRISE\",\"dbName\":null}],\"dbName\":null},\"SkillLevel\":{\"values\":[{\"name\":\"BEGINNER\",\"dbName\":null},{\"name\":\"INTERMEDIATE\",\"dbName\":null},{\"name\":\"ADVANCED\",\"dbName\":null},{\"name\":\"EXPERT\",\"dbName\":null}],\"dbName\":null},\"ActionType\":{\"values\":[{\"name\":\"VIEW_ENTITY\",\"dbName\":null},{\"name\":\"CLICK_ENTITY_LINK\",\"dbName\":null},{\"name\":\"SAVE_ENTITY\",\"dbName\":null},{\"name\":\"UNSAVE_ENTITY\",\"dbName\":null},{\"name\":\"SUBMIT_REVIEW\",\"dbName\":null},{\"name\":\"VOTE_REVIEW\",\"dbName\":null},{\"name\":\"FOLLOW_TAG\",\"dbName\":null},{\"name\":\"UNFOLLOW_TAG\",\"dbName\":null},{\"name\":\"FOLLOW_CATEGORY\",\"dbName\":null},{\"name\":\"UNFOLLOW_CATEGORY\",\"dbName\":null},{\"name\":\"SEARCH\",\"dbName\":null},{\"name\":\"LOGIN\",\"dbName\":null},{\"name\":\"LOGOUT\",\"dbName\":null},{\"name\":\"SIGNUP\",\"dbName\":null},{\"name\":\"UPDATE_PROFILE\",\"dbName\":null},{\"name\":\"GRANT_BADGE\",\"dbName\":null},{\"name\":\"REVOKE_BADGE\",\"dbName\":null}],\"dbName\":null},\"BadgeScope\":{\"values\":[{\"name\":\"USER\",\"dbName\":null},{\"name\":\"ENTITY\",\"dbName\":null}],\"dbName\":null}},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)


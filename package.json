{"name": "ai-navigator", "version": "1.0.0", "description": "AI Navigator is an intelligent platform designed to help users navigate the complex and rapidly growing Artificial Intelligence ecosystem. It acts as a central hub for discovering, evaluating, and understanding AI tools, agencies, and related resources. The core goal is to move beyond simple directories by providing AI-powered guidance and curated insights, starting with an \"Intelligent MVP\" focused on discovery and basic evaluation.", "author": "Colum", "scripts": {"dev": "next dev", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd", "prisma:generate": "npx prisma generate", "build": "npx prisma generate && tsc -p tsconfig.build.json", "postinstall": "npx prisma generate", "start": "node dist/main", "start:dev": "nest start --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage --passWithNoTests", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "start-server-and-test 'node server.js' http://localhost:3000 cypress:run", "cypress:open": "cypress open", "cypress:run": "cypress run"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@nestjs/common": "^11.0.21", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.21", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.21", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.7.0", "@supabase/supabase-js": "^2.49.4", "boxen": "^8.0.1", "chalk": "^4.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.3", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "module-alias": "^2.2.3", "next": "^15.3.1", "openai": "^4.89.0", "ora": "^8.2.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/testing": "^11.1.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/passport-jwt": "^4.0.1", "@types/react": "^19.1.2", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "cypress": "^14.3.1", "eslint": "^9.26.0", "jest": "^29.7.0", "prisma": "^6.7.0", "start-server-and-test": "^2.0.11", "supabase": "^2.22.12", "supertest": "^7.1.1", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1"}, "prisma": {"schema": "prisma/schema.prisma", "seed": "ts-node prisma/seed.ts"}, "_moduleAliases": {"@generated-prisma": "generated/prisma", "generated/prisma": "generated/prisma"}}
import { ExceptionFilter, Catch, ArgumentsHost, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { Prisma } from '../../../generated/prisma'; // Ensure this path is correct for your project
import { HttpAdapterHost } from '@nestjs/core';

@Catch(Prisma.PrismaClientKnownRequestError, Prisma.PrismaClientValidationError)
export class PrismaClientExceptionFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: Prisma.PrismaClientKnownRequestError | Prisma.PrismaClientValidationError, host: ArgumentsHost) {
    const { httpAdapter } = this.httpAdapterHost;
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest(); // For logging path if needed

    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'An unexpected internal server error occurred.';
    let errorType = 'InternalServerError';
    let errorDetails: any = {};

    if (exception instanceof Prisma.PrismaClientKnownRequestError) {
      errorType = 'DatabaseError'; // More specific type
      switch (exception.code) {
        case 'P2000':
          statusCode = HttpStatus.BAD_REQUEST;
          message = `The provided value for the field '${exception.meta?.target}' is too long.`;
          errorDetails = { field: exception.meta?.target, reason: 'value_too_long' };
          break;
        case 'P2002':
          statusCode = HttpStatus.CONFLICT;
          message = `A record with this '${(exception.meta?.target as string[])?.join(', ')}' already exists.`;
          errorDetails = { fields: exception.meta?.target, reason: 'unique_constraint_failed' };
          break;
        case 'P2003': // Foreign key constraint failed
            statusCode = HttpStatus.CONFLICT; // Or BAD_REQUEST if the ID provided was simply invalid
            message = `A foreign key constraint failed on the field '${exception.meta?.field_name}'. The related record may not exist or the operation is not allowed.`;
            errorDetails = { field: exception.meta?.field_name, reason: 'foreign_key_constraint_failed' };
            break;
        case 'P2014':
            statusCode = HttpStatus.CONFLICT;
            message = `The change you are trying to make would violate the required relation '${exception.meta?.relation_name}' between the '${exception.meta?.model_a_name}' and '${exception.meta?.model_b_name}' models.`;
            errorDetails = { 
              relation: exception.meta?.relation_name, 
              models: [exception.meta?.model_a_name, exception.meta?.model_b_name],
              reason: 'relation_violation'
            };
            break;
        case 'P2025':
          statusCode = HttpStatus.NOT_FOUND;
          message = 'The requested resource or a related required resource was not found.';
          errorDetails = { cause: exception.meta?.cause || 'Required record not found.', reason: 'record_not_found' };
          break;
        default:
          console.error(`Unhandled PrismaClientKnownRequestError: Code ${exception.code}\n${exception.message}`, exception.stack);
          message = `A database error occurred (Code: ${exception.code}). Please check server logs.`;
          errorDetails = { code: exception.code };
          break;
      }
    } else if (exception instanceof Prisma.PrismaClientValidationError) {
      statusCode = HttpStatus.BAD_REQUEST;
      message = 'Input validation failed. Please check your data and try again.';
      errorType = 'ValidationError';
      // Prisma's validation error messages can be verbose and expose schema details.
      // Log the full error for debugging, but provide a more generic client message.
      console.error('PrismaClientValidationError:', exception.message, exception.stack);
      errorDetails = { reason: "Invalid input data based on schema." };
    } else {
      // Should not happen if @Catch is specific, but as a fallback
      console.error('Unhandled error in PrismaClientExceptionFilter:', exception, (exception as any).stack);
    }

    const responseBody = {
      statusCode,
      message,
      error: errorType === 'InternalServerError' && statusCode === HttpStatus.INTERNAL_SERVER_ERROR ? 'Internal Server Error' : errorType,
      details: errorDetails,
      timestamp: new Date().toISOString(),
      path: request.url, 
    };

    httpAdapter.reply(response, responseBody, statusCode);
  }
} 
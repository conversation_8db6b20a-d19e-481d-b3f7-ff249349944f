import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger('HTTP');

  use(req: Request, res: Response, next: NextFunction) {
    const { method, originalUrl, ip, headers } = req;
    const userAgent = headers['user-agent'] || '';
    const requestTimestamp = new Date(); // Capture timestamp at the beginning

    // Log when request is received
    this.logger.log(
      `Incoming Request: ${method} ${originalUrl} - IP: ${ip} - UserAgent: "${userAgent}"`,
    );

    res.on('finish', () => {
      const { statusCode } = res;
      const userId = req.user ? (req.user as any).id : 'Guest'; // Assuming req.user has an id
      const processingTime = Date.now() - requestTimestamp.getTime();

      this.logger.log(
        `Outgoing Response: ${method} ${originalUrl} ${statusCode} - UserID: ${userId} - IP: ${ip} - ProcessingTime: ${processingTime}ms`,
      );
    });

    next();
  }
} 
import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, Req, ParseUUIDPipe, HttpCode, HttpStatus, NotFoundException } from '@nestjs/common';
import { EntitiesService } from './entities.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard'; // For admin-only operations like delete
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiOkResponse, ApiBody } from '@nestjs/swagger';
import { User as UserModelPrisma, EntityStatus, Prisma, EntityType as EntityTypeModel, Category as CategoryModel, Tag as TagModel, Feature as FeatureModelPrisma, EntityDetailsTool as EntityDetailsToolModel, EntityDetailsCourse as EntityDetailsCourseModel, EntityDetailsDataset as EntityDetailsDatasetModel, EntityDetailsResearchPaper as EntityDetailsResearchPaperModel, EntityDetailsSoftware as EntityDetailsSoftwareModel, EntityDetailsModel as EntityDetailsModelPrisma, EntityDetailsProjectReference as EntityDetailsProjectReferenceModel, EntityDetailsServiceProvider as EntityDetailsServiceProviderModel, EntityDetailsInvestor as EntityDetailsInvestorModel, EntityDetailsCommunity as EntityDetailsCommunityModel, EntityDetailsEvent as EntityDetailsEventModel, EntityDetailsJob as EntityDetailsJobModel, EntityDetailsGrant as EntityDetailsGrantModel, EntityDetailsBounty as EntityDetailsBountyModel, EntityDetailsHardware as EntityDetailsHardwareModel, EntityDetailsNews as EntityDetailsNewsModel, EntityDetailsBook as EntityDetailsBookModel, EntityDetailsPodcast as EntityDetailsPodcastModel, EntityDetailsNewsletter as EntityDetailsNewsletterModel, EntityDetailsPlatform as EntityDetailsPlatformModel, EntityDetailsAgency as EntityDetailsAgencyModel, EntityDetailsContentCreator as EntityDetailsContentCreatorModel } from '../../generated/prisma'; // To type req.user & models
import { ListEntitiesDto } from './dto/list-entities.dto';
import { EntityResponseDto } from './dto/entity-response.dto';
import { PaginatedEntityResponseDto } from './dto/paginated-entity-response.dto';
import { UserProfileMinimalDto } from '../auth/dto/user-profile-minimal.dto';
import { EntityTypeResponseDto } from '../entity-types/dto/entity-type-response.dto';
import { CategoryResponseDto } from '../categories/dto/category-response.dto';
import { TagResponseDto } from '../tags/dto/tag-response.dto';
import { FeatureResponseDto } from '../features/dto/feature-response.dto';

// Import all specific detail DTOs for mapping
import { ToolDetailsResponseDto } from './dto/details/tool-details-response.dto';
import { CourseDetailsResponseDto } from './dto/details/course-details-response.dto';
import { DatasetDetailsResponseDto } from './dto/details/dataset-details-response.dto';
import { ResearchPaperDetailsResponseDto } from './dto/details/research-paper-details-response.dto';
import { SoftwareDetailsResponseDto } from './dto/details/software-details-response.dto';
import { ModelDetailsResponseDto } from './dto/details/model-details-response.dto';
import { ProjectReferenceDetailsResponseDto } from './dto/details/project-reference-details-response.dto';
import { ServiceProviderDetailsResponseDto } from './dto/details/service-provider-details-response.dto';
import { InvestorDetailsResponseDto } from './dto/details/investor-details-response.dto';
import { CommunityDetailsResponseDto } from './dto/details/community-details-response.dto';
import { EventDetailsResponseDto } from './dto/details/event-details-response.dto';
import { JobDetailsResponseDto } from './dto/details/job-details-response.dto';
import { GrantDetailsResponseDto } from './dto/details/grant-details-response.dto';
import { BountyDetailsResponseDto } from './dto/details/bounty-details-response.dto';
import { HardwareDetailsResponseDto } from './dto/details/hardware-details-response.dto';
import { NewsDetailsResponseDto } from './dto/details/news-details-response.dto';
import { BookDetailsResponseDto } from './dto/details/book-details-response.dto';
import { PodcastDetailsResponseDto } from './dto/details/podcast-details-response.dto';
import { NewsletterDetailsResponseDto } from './dto/details/newsletter-details-response.dto';
import { PlatformDetailsResponseDto } from './dto/details/platform-details-response.dto';
import { AgencyDetailsResponseDto } from './dto/details/agency-details-response.dto';
import { ContentCreatorDetailsResponseDto } from './dto/details/content-creator-details-response.dto';

// Define the type for Prisma Entity with all relations for mapping
type PrismaEntityWithFullRelations = Prisma.EntityGetPayload<{
  include: {
    entityType: true;
    submitter: true;
    entityCategories: { include: { category: true } };
    entityTags: { include: { tag: true } };
    entityFeatures: { include: { feature: true } };
    // NOTE: Temporarily removing all 'entityDetails...' includes from this type definition
    // to work around a persistent TypeScript type inference issue.
    // The service still fetches this data, and we will cast to 'any' during mapping.
  }
}>;

@ApiTags('Entities')
@Controller('entities')
export class EntitiesController {
  constructor(private readonly entitiesService: EntitiesService) {}

  // --- Helper Mapping Functions ---
  private mapUserToMinimalDto(user: UserModelPrisma): UserProfileMinimalDto | null {
    if (!user) return null;
    const minimalDto: UserProfileMinimalDto = {
      id: user.authUserId, // This is the Supabase Auth User ID
      email: user.email,
      created_at: user.createdAt.toISOString(),
      last_sign_in_at: user.lastLogin ? user.lastLogin.toISOString() : null,
      user_metadata: {
        username: user.username,
        display_name: user.displayName,
        // Adding profilePictureUrl to user_metadata as UserProfileMinimalDto doesn't have a direct field
        profile_picture_url: user.profilePictureUrl,
        // We can also map our internal user ID here if needed for frontend reference
        internal_user_id: user.id 
      },
      // app_metadata can be omitted if not directly relevant or mapped from user.role/status
      // app_metadata: {
      //   role: user.role,
      //   status: user.status
      // }
    };
    return minimalDto;
  }

  private mapEntityTypeToDto(entityType: EntityTypeModel): EntityTypeResponseDto | null {
    if (!entityType) return null;
    return {
      id: entityType.id,
      name: entityType.name,
      slug: entityType.slug,
      description: entityType.description,
      iconUrl: entityType.iconUrl,
      createdAt: entityType.createdAt,
      updatedAt: entityType.updatedAt,
    };
  }

  private mapCategoryToDto(category: CategoryModel): CategoryResponseDto | null {
    if (!category) return null;
    return {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      iconUrl: category.iconUrl,
      parentCategoryId: category.parentCategoryId,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    };
  }

  private mapTagToDto(tag: TagModel): TagResponseDto | null {
    if (!tag) return null;
    return {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
    };
  }

  private mapFeatureToDto(feature: FeatureModelPrisma): FeatureResponseDto | null {
    if (!feature) return null;
    return {
      id: feature.id,
      name: feature.name,
      slug: feature.slug,
      description: feature.description,
      iconUrl: feature.iconUrl,
      createdAt: feature.createdAt,
      updatedAt: feature.updatedAt,
    };
  }

  private mapToolDetailsToDto(details: any | null): ToolDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      technicalLevel: details.technicalLevel,
      learningCurve: details.learningCurve,
      targetAudience: details.targetAudience,
      hasApi: details.hasApi, // Assuming details.hasApi is boolean and not boolean | null
      apiDocumentationUrl: details.apiDocumentationUrl,
      apiSandboxUrl: details.apiSandboxUrl,
      keyFeatures: details.keyFeatures, // Prisma JSON fields are 'any' or 'JsonValue'
      useCases: details.useCases,
      pricingModel: details.pricingModel,
      priceRange: details.priceRange,
      pricingDetails: details.pricingDetails,
      pricingUrl: details.pricingUrl,
      hasFreeTier: details.hasFreeTier, // Assuming details.hasFreeTier is boolean
      platforms: details.platforms,
      integrations: details.integrations,
      // For supportedLanguages, we'll use programmingLanguages from the model as a starting point.
      // More complex logic might be needed if frameworks/libraries should be merged.
      supportedLanguages: details.programmingLanguages, // Assuming programmingLanguages is Prisma.JsonValue (e.g., string[])
      currentVersion: details.currentVersion,
      lastVersionUpdateDate: details.lastVersionUpdateDate,
      changelogUrl: details.changelogUrl,
      // Note: Fields like supportEmail, hasLiveChat, communityUrl from the model are not in ToolDetailsResponseDto
    };
  }

  private mapCourseDetailsToDto(details: any | null): CourseDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      instructorName: details.instructorName,
      durationText: details.durationText,
      skillLevel: details.skillLevel,
      prerequisites: details.prerequisites,
      syllabusUrl: details.syllabusUrl,
      enrollmentCount: details.enrollmentCount,
      certificateAvailable: details.certificateAvailable,
    };
  }

  private mapDatasetDetailsToDto(details: any | null): DatasetDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      format: details.format,
      sourceUrl: details.sourceUrl,
      license: details.license,
      sizeInBytes: details.sizeInBytes,
      description: details.description,
      accessNotes: details.accessNotes,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapResearchPaperDetailsToDto(details: any | null): ResearchPaperDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      publicationDate: details.publicationDate,
      doi: details.doi,
      authors: details.authors,
      abstract: details.abstract,
      journalOrConference: details.journalOrConference,
      publicationUrl: details.publicationUrl,
      citationCount: details.citationCount,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapSoftwareDetailsToDto(details: any | null): SoftwareDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      repositoryUrl: details.repositoryUrl,
      licenseType: details.licenseType,
      programmingLanguages: details.programmingLanguages,
      platformCompatibility: details.platformCompatibility,
      currentVersion: details.currentVersion,
      releaseDate: details.releaseDate,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapModelDetailsToDto(details: any | null): ModelDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      modelArchitecture: details.modelArchitecture,
      parametersCount: details.parametersCount,
      trainingDataset: details.trainingDataset,
      performanceMetrics: details.performanceMetrics,
      modelUrl: details.modelUrl,
      license: details.license,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapProjectReferenceDetailsToDto(details: any | null): ProjectReferenceDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      projectStatus: details.projectStatus,
      sourceCodeUrl: details.sourceCodeUrl,
      liveDemoUrl: details.liveDemoUrl,
      technologies: details.technologies,
      projectGoals: details.projectGoals,
      contributors: details.contributors,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapServiceProviderDetailsToDto(details: any | null): ServiceProviderDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      serviceAreas: details.serviceAreas,
      caseStudiesUrl: details.caseStudiesUrl,
      consultationBookingUrl: details.consultationBookingUrl,
      industrySpecializations: details.industrySpecializations,
      companySizeFocus: details.companySizeFocus,
      hourlyRateRange: details.hourlyRateRange,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapInvestorDetailsToDto(details: any | null): InvestorDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      investmentFocusAreas: details.investmentFocusAreas,
      portfolioUrl: details.portfolioUrl,
      typicalInvestmentSize: details.typicalInvestmentSize,
      investmentStages: details.investmentStages,
      contactEmail: details.contactEmail,
      preferredCommunication: details.preferredCommunication,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapCommunityDetailsToDto(details: any | null): CommunityDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      platform: details.platform,
      memberCount: details.memberCount,
      focusTopics: details.focusTopics,
      rulesUrl: details.rulesUrl,
      inviteUrl: details.inviteUrl,
      mainChannelUrl: details.mainChannelUrl,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapEventDetailsToDto(details: any | null): EventDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      eventType: details.eventType,
      startDate: details.startDate,
      endDate: details.endDate,
      location: details.location,
      registrationUrl: details.registrationUrl,
      speakerList: details.speakerList,
      agendaUrl: details.agendaUrl,
      price: details.price,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapJobDetailsToDto(details: any | null): JobDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      jobTitle: details.jobTitle,
      companyName: details.companyName,
      locationType: details.locationType,
      salaryRange: details.salaryRange,
      applicationUrl: details.applicationUrl,
      jobDescription: details.jobDescription,
      experienceLevel: details.experienceLevel,
      employmentType: details.employmentType,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapGrantDetailsToDto(details: any | null): GrantDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      grantingInstitution: details.grantingInstitution,
      eligibilityCriteria: details.eligibilityCriteria,
      applicationDeadline: details.applicationDeadline,
      fundingAmount: details.fundingAmount,
      applicationUrl: details.applicationUrl,
      grantFocusArea: details.grantFocusArea,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapBountyDetailsToDto(details: any | null): BountyDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      bountyIssuer: details.bountyIssuer,
      rewardAmount: details.rewardAmount,
      requirements: details.requirements,
      submissionDeadline: details.submissionDeadline,
      platformUrl: details.platformUrl,
      difficultyLevel: details.difficultyLevel,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapHardwareDetailsToDto(details: any | null): HardwareDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      hardwareType: details.hardwareType,
      specifications: details.specifications,
      manufacturer: details.manufacturer,
      releaseDate: details.releaseDate,
      priceRange: details.priceRange,
      datasheetUrl: details.datasheetUrl,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapNewsDetailsToDto(details: any | null): NewsDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      publicationDate: details.publicationDate,
      sourceName: details.sourceName,
      articleUrl: details.articleUrl,
      author: details.author,
      summary: details.summary,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapBookDetailsToDto(details: any | null): BookDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      authorNames: details.authorNames,
      isbn: details.isbn,
      publisher: details.publisher,
      publicationYear: details.publicationYear,
      pageCount: details.pageCount,
      summary: details.summary,
      purchaseUrl: details.purchaseUrl,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapPodcastDetailsToDto(details: any | null): PodcastDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      hostNames: details.hostNames,
      averageEpisodeLength: details.averageEpisodeLength,
      mainTopics: details.mainTopics,
      listenUrl: details.listenUrl,
      frequency: details.frequency,
      primaryLanguage: details.primaryLanguage,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapNewsletterDetailsToDto(details: any | null): NewsletterDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      frequency: details.frequency,
      mainTopics: details.mainTopics,
      archiveUrl: details.archiveUrl,
      subscribeUrl: details.subscribeUrl,
      authorName: details.authorName,
      subscriberCount: details.subscriberCount,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapPlatformDetailsToDto(details: any | null): PlatformDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      platformType: details.platformType,
      keyServices: details.keyServices,
      documentationUrl: details.documentationUrl,
      pricingModel: details.pricingModel,
      slaUrl: details.slaUrl,
      supportedRegions: details.supportedRegions,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapAgencyDetailsToDto(details: any | null): AgencyDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      servicesOffered: details.servicesOffered,
      industryFocus: details.industryFocus,
      targetClientSize: details.targetClientSize,
      targetAudience: details.targetAudience,
      locationSummary: details.locationSummary,
      portfolioUrl: details.portfolioUrl,
      pricingInfo: details.pricingInfo,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  private mapContentCreatorDetailsToDto(details: any | null): ContentCreatorDetailsResponseDto | null {
    if (!details) return null;
    return {
      entityId: details.entityId,
      creatorName: details.creatorName,
      primaryPlatform: details.primaryPlatform,
      focusAreas: details.focusAreas,
      followerCount: details.followerCount,
      exampleContentUrl: details.exampleContentUrl,
      createdAt: details.createdAt,
      updatedAt: details.updatedAt,
    };
  }

  // --- Main Entity to DTO Mapper ---
  private mapEntityToResponseDto(entity: PrismaEntityWithFullRelations): EntityResponseDto {
    const responseDto = new EntityResponseDto();
    responseDto.id = entity.id;
    responseDto.name = entity.name;
    responseDto.websiteUrl = entity.websiteUrl;
    responseDto.entityType = this.mapEntityTypeToDto(entity.entityType);
    responseDto.shortDescription = entity.shortDescription;
    responseDto.description = entity.description;
    responseDto.logoUrl = entity.logoUrl;
    responseDto.documentationUrl = entity.documentationUrl;
    responseDto.contactUrl = entity.contactUrl;
    responseDto.privacyPolicyUrl = entity.privacyPolicyUrl;
    responseDto.foundedYear = entity.foundedYear;
    responseDto.status = entity.status;
    responseDto.socialLinks = entity.socialLinks;
    responseDto.submitter = this.mapUserToMinimalDto(entity.submitter);
    responseDto.legacyId = entity.legacyId;
    responseDto.reviewCount = entity.reviewCount;
    responseDto.avgRating = entity.avgRating;
    responseDto.createdAt = entity.createdAt;
    responseDto.updatedAt = entity.updatedAt;
    responseDto.categories = entity.entityCategories?.map(ec => this.mapCategoryToDto(ec.category)).filter(Boolean) as CategoryResponseDto[] || [];
    responseDto.tags = entity.entityTags?.map(et => this.mapTagToDto(et.tag)).filter(Boolean) as TagResponseDto[] || [];
    responseDto.features = entity.entityFeatures?.map(ef => this.mapFeatureToDto(ef.feature)).filter(Boolean) as FeatureResponseDto[] || [];

    // Dynamic details mapping
    switch (entity.entityType?.slug) {
      case 'ai-tool':
        responseDto.details = this.mapToolDetailsToDto((entity as any).entityDetailsTool);
        break;
      case 'online-course':
        responseDto.details = this.mapCourseDetailsToDto((entity as any).entityDetailsCourse);
        break;
      case 'dataset':
        responseDto.details = this.mapDatasetDetailsToDto((entity as any).entityDetailsDataset);
        break;
      case 'research-paper':
        responseDto.details = this.mapResearchPaperDetailsToDto((entity as any).entityDetailsResearchPaper);
        break;
      case 'software':
        responseDto.details = this.mapSoftwareDetailsToDto((entity as any).entityDetailsSoftware);
        break;
      case 'ai-model':
      case 'model':
        responseDto.details = this.mapModelDetailsToDto((entity as any).entityDetailsModel);
        break;
      case 'project-reference':
        responseDto.details = this.mapProjectReferenceDetailsToDto((entity as any).entityDetailsProjectReference);
        break;
      case 'service-provider':
        responseDto.details = this.mapServiceProviderDetailsToDto((entity as any).entityDetailsServiceProvider);
        break;
      case 'investor':
        responseDto.details = this.mapInvestorDetailsToDto((entity as any).entityDetailsInvestor);
        break;
      case 'community':
        responseDto.details = this.mapCommunityDetailsToDto((entity as any).entityDetailsCommunity);
        break;
      case 'event':
        responseDto.details = this.mapEventDetailsToDto((entity as any).entityDetailsEvent);
        break;
      case 'job-listing':
      case 'job':
        responseDto.details = this.mapJobDetailsToDto((entity as any).entityDetailsJob);
        break;
      case 'grant':
        responseDto.details = this.mapGrantDetailsToDto((entity as any).entityDetailsGrant);
        break;
      case 'bounty':
        responseDto.details = this.mapBountyDetailsToDto((entity as any).entityDetailsBounty);
        break;
      case 'hardware':
        responseDto.details = this.mapHardwareDetailsToDto((entity as any).entityDetailsHardware);
        break;
      case 'news':
        responseDto.details = this.mapNewsDetailsToDto((entity as any).entityDetailsNews);
        break;
      case 'book':
        responseDto.details = this.mapBookDetailsToDto((entity as any).entityDetailsBook);
        break;
      case 'podcast':
        responseDto.details = this.mapPodcastDetailsToDto((entity as any).entityDetailsPodcast);
        break;
      case 'newsletter':
        responseDto.details = this.mapNewsletterDetailsToDto((entity as any).entityDetailsNewsletter);
        break;
      case 'platform':
        responseDto.details = this.mapPlatformDetailsToDto((entity as any).entityDetailsPlatform);
        break;
      case 'agency':
        responseDto.details = this.mapAgencyDetailsToDto((entity as any).entityDetailsAgency);
        break;
      case 'content-creator':
        responseDto.details = this.mapContentCreatorDetailsToDto((entity as any).entityDetailsContentCreator);
        break;
      default:
        responseDto.details = null;
    }
    return responseDto;
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new entity (submission for review)' })
  @ApiBody({ type: CreateEntityDto, description: 'Data to create a new entity' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'The entity has been successfully submitted for review.', type: EntityResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data. Please check the request body for errors.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden. You do not have permission to perform this action.' })
  @ApiResponse({ status: HttpStatus.CONFLICT, description: 'Conflict. An entity with some unique identifier (e.g., name or URL) might already exist, or a related resource was not found.' })
  @ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' })
  async create(@Body() createEntityDto: CreateEntityDto, @Req() req: { user: UserModelPrisma }): Promise<EntityResponseDto> {
    const entity = await this.entitiesService.create(createEntityDto, req.user);
    // Assuming 'create' service method returns the full entity with relations after creation for mapping
    // This might need adjustment if 'create' returns a simpler object initially
    const fullEntity = await this.entitiesService.findOne(entity.id); // Re-fetch to ensure all relations are loaded
    if (!fullEntity) throw new NotFoundException('Failed to retrieve created entity details.');
    return this.mapEntityToResponseDto(fullEntity as PrismaEntityWithFullRelations);
  }

  @Get()
  @ApiOperation({ summary: 'List all entities with filtering, pagination, and sorting' })
  @ApiOkResponse({ description: 'A paginated list of entities matching the criteria.', type: PaginatedEntityResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters. Please check the filter or pagination values.' })
  @ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: Prisma.SortOrder, description: 'Sort order' })
  @ApiQuery({ name: 'searchTerm', required: false, type: String, description: 'Search term for name, description' })
  @ApiQuery({ name: 'status', required: false, enum: EntityStatus, description: 'Filter by status' })
  @ApiQuery({ name: 'entityTypeId', required: false, type: String, description: 'Filter by entity type ID (UUID)' })
  @ApiQuery({ name: 'categoryIds', required: false, type: [String], description: 'Filter by category IDs (UUIDs, comma-separated or multiple params)', style: 'form', explode: false })
  @ApiQuery({ name: 'tagIds', required: false, type: [String], description: 'Filter by tag IDs (UUIDs, comma-separated or multiple params)', style: 'form', explode: false })
  @ApiQuery({ name: 'submitterId', required: false, type: String, description: 'Filter by submitter user ID (UUID)' })
  async findAll(@Query() listEntitiesDto: ListEntitiesDto): Promise<PaginatedEntityResponseDto> {
    const result = await this.entitiesService.findAll(listEntitiesDto);
    return {
      data: result.data.map(entity => this.mapEntityToResponseDto(entity as PrismaEntityWithFullRelations)),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      search: listEntitiesDto.searchTerm,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single entity by its ID' })
  @ApiParam({ name: 'id', required: true, description: 'UUID of the entity to retrieve', type: String, format: 'uuid' })
  @ApiOkResponse({ description: 'The entity was found and returned.', type: EntityResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid UUID format for entity ID.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found.' })
  @ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<EntityResponseDto> {
    const entity = await this.entitiesService.findOne(id);
    if (!entity) {
      throw new NotFoundException(`Entity with ID ${id} not found.`);
    }
    return this.mapEntityToResponseDto(entity as PrismaEntityWithFullRelations);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an existing entity. Submitter can update PENDING/ACTIVE/REJECTED/INACTIVE entities (ACTIVE changes to PENDING). Admin can update any.' })
  @ApiParam({ name: 'id', required: true, description: 'UUID of the entity to update', type: String, format: 'uuid' })
  @ApiBody({ type: UpdateEntityDto, description: 'Data to update the entity' })
  @ApiOkResponse({ description: 'The entity has been successfully updated.', type: EntityResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data or invalid UUID format.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden. You do not have permission to update this entity or perform this status transition.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found, or a related resource (like a category/tag ID) is invalid.' })
  @ApiResponse({ status: HttpStatus.CONFLICT, description: 'Conflict. An update would violate a unique constraint (e.g., name or URL).' })
  @ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateEntityDto: UpdateEntityDto, @Req() req: { user: UserModelPrisma }): Promise<EntityResponseDto> {
    const entity = await this.entitiesService.update(id, updateEntityDto, req.user);
    if (!entity) {
      // This case might be handled by the service throwing an error already
      throw new NotFoundException(`Entity with ID ${id} not found or update failed.`);
    }
    // Re-fetch to ensure all relations are loaded for complete mapping
    const fullEntity = await this.entitiesService.findOne(entity.id);
    if (!fullEntity) throw new NotFoundException('Failed to retrieve updated entity details.');
    return this.mapEntityToResponseDto(fullEntity as PrismaEntityWithFullRelations);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Archive an entity by ID (Admin only). This performs a soft delete.' })
  @ApiParam({ name: 'id', required: true, description: 'UUID of the entity to archive', type: String, format: 'uuid' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: 'The entity has been successfully archived.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid UUID format for entity ID.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden. Only administrators can archive entities.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found.' })
  @ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @Req() req: { user: UserModelPrisma }) {
    await this.entitiesService.remove(id, req.user); // req.user is passed for potential logging or audit, though AdminGuard already checks role
  }
} 
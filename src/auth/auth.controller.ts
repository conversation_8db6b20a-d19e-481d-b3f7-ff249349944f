import { Controller, Post, Body, ValidationPipe, HttpCode, HttpStatus, Get, UseGuards, Req, UnauthorizedException, Headers, InternalServerErrorException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { RegisterAuthDto } from './dto/register-auth.dto';
import { LoginAuthDto } from './dto/login-auth.dto';
import { User as SupabaseUserType, Session as SupabaseSessionType } from '@supabase/supabase-js'; // Renamed to avoid conflict
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { Request } from 'express';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ResendConfirmationDto } from './dto/resend-confirmation.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { SignUpResponseDto } from './dto/signup-response.dto';
import { LoginResponseDto, SessionDto } from './dto/login-response.dto';
import { UserProfileMinimalDto } from './dto/user-profile-minimal.dto';
import { GetUser } from './decorators/get-user.decorator';
import { User as PrismaUserProfile } from '../../generated/prisma';
import { ReqUserObject } from './strategies/jwt.strategy'; // Import the new interface

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Register a new user',
    description: 'Creates a new user account. An email confirmation will be sent to the provided email address. The username and email must be unique.'
  })
  @ApiBody({ type: RegisterAuthDto })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'User registered successfully. Please check your email to confirm your registration.', type: SignUpResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., email format, password strength, missing fields).' })
  @ApiResponse({ status: HttpStatus.CONFLICT, description: 'Email or username already exists.' })
  async signUp(@Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true, transform: true })) registerAuthDto: RegisterAuthDto): Promise<SignUpResponseDto> {
    const { user: supabaseUser } = await this.authService.signUp(registerAuthDto);
    
    if (!supabaseUser) {
      throw new InternalServerErrorException('User registration completed but user data is unexpectedly missing.');
    }
    
    const safeUser: UserProfileMinimalDto = {
      id: supabaseUser.id,
      email: supabaseUser.email || '',
      created_at: supabaseUser.created_at,
      last_sign_in_at: supabaseUser.last_sign_in_at || null,
      app_metadata: supabaseUser.app_metadata || undefined,
      user_metadata: supabaseUser.user_metadata || undefined,
    };

    return {
        message: 'User registered successfully. Please check your email to confirm your registration.',
        user: safeUser,
    };
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Log in an existing user',
    description: 'Authenticates a user and returns a session object including JWT access and refresh tokens.'
  })
  @ApiBody({ type: LoginAuthDto })
  @ApiResponse({ status: HttpStatus.OK, description: 'User logged in successfully.', type: LoginResponseDto })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Invalid credentials or email not confirmed.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data.' })
  async signIn(@Body() loginAuthDto: LoginAuthDto): Promise<LoginResponseDto> {
    const { user, session } = await this.authService.signIn(loginAuthDto);

    const safeUser: UserProfileMinimalDto = {
      id: user.id,
      email: user.email || '',
      created_at: user.created_at,
      last_sign_in_at: user.last_sign_in_at || null,
      app_metadata: user.app_metadata || undefined,
      user_metadata: user.user_metadata || undefined,
    };
    
    let sessionDto: SessionDto | null = null;
    if (session) {
      const sessionUser: UserProfileMinimalDto = {
        id: session.user.id,
        email: session.user.email || '',
        created_at: session.user.created_at,
        last_sign_in_at: session.user.last_sign_in_at || null,
        app_metadata: session.user.app_metadata || undefined,
        user_metadata: session.user.user_metadata || undefined,
      };
      sessionDto = {
        access_token: session.access_token,
        refresh_token: session.refresh_token,
        token_type: session.token_type,
        expires_in: session.expires_in,
        expires_at: session.expires_at || undefined,
        user: sessionUser,
      };
    }

    return {
      message: 'User logged in successfully.',
      user: safeUser,
      session: sessionDto,
    };
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Log out the current user', 
    description: 'Invalidates the current user\'s session. Requires a valid JWT Bearer token.'
  })
  @ApiBearerAuth()
  @ApiResponse({ status: HttpStatus.OK, description: 'User logged out successfully.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated or token invalid.' })
  async logout(@Req() req: Request): Promise<{ message: string }> {
    // Guard ensures user is authenticated; service Supabase client should be correctly scoped.
    await this.authService.signOut(); // Reverted to no arguments
    return { message: 'User logged out successfully.' };
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Request a password reset link',
    description: 'Sends a password reset link to the user\'s registered email address if the account exists.'
  })
  @ApiBody({ type: ForgotPasswordDto })
  @ApiResponse({ status: HttpStatus.OK, description: 'If an account with this email exists, a password reset link has been sent.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., email format).' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Email not found (though the response is generic for security).' }) 
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<{ message:string }> {
    await this.authService.forgotPassword(forgotPasswordDto);
    return { message: 'If an account with this email exists, a password reset link has been sent.' };
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Reset user password using a token from email',
    description: 'Allows a user to set a new password after following a password reset link sent to their email. The client should include the temporary access token (obtained from the password recovery URL fragment or specific auth event) as a Bearer token in the Authorization header for this request.'
  })
  @ApiBody({ type: ResetPasswordDto })
  @ApiBearerAuth()
  @ApiResponse({ status: HttpStatus.OK, description: 'Password has been reset successfully.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., passwords don\'t match, password too weak).' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Invalid or expired password reset token.' })
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
    @Headers('authorization') authHeader?: string
  ): Promise<{ message: string }> {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('Authorization header with Bearer token is required for password reset.');
    }
    const accessToken = authHeader.split(' ')[1];
    if (!accessToken) {
      throw new UnauthorizedException('Bearer token is missing or malformed.');
    }
    await this.authService.resetPassword(resetPasswordDto, accessToken);
    return { message: 'Password has been reset successfully.' };
  }

  @Post('resend-confirmation')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Resend email confirmation link',
    description: 'Sends a new email confirmation link to the user\'s email address if their account exists and is pending confirmation.'
  })
  @ApiBody({ type: ResendConfirmationDto })
  @ApiResponse({ status: HttpStatus.OK, description: 'If your account exists and requires confirmation, a new confirmation email has been sent.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., email format).' })
  @ApiResponse({ status: HttpStatus.CONFLICT, description: 'Email already confirmed or recent resend attempt.' })
  async resendConfirmation(@Body() resendConfirmationDto: ResendConfirmationDto): Promise<{ message: string }> {
    await this.authService.resendConfirmation(resendConfirmationDto);
    return { message: 'If your account exists and requires confirmation, a new confirmation email has been sent.' };
  }

  @Post('sync-profile')
  @UseGuards(JwtAuthGuard) 
  @ApiOperation({ summary: 'Synchronize authenticated user profile with local database' })
  @ApiResponse({ status: 200, description: 'Profile synced successfully', type: UserProfileMinimalDto }) // Adjust response DTO as needed
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiBearerAuth()
  async syncProfile(
    @GetUser() reqUser: ReqUserObject, // Get the new ReqUserObject
  ): Promise<PrismaUserProfile> { 
    // console.log('[AuthController] /sync-profile called with reqUser:', JSON.stringify(reqUser, null, 2)); // REMOVE THIS

    // Construct the payload for the service
    const servicePayload = {
      id: reqUser.authData.sub, // Map 'sub' to 'id'
      email: reqUser.authData.email,
      user_metadata: reqUser.authData.user_metadata,
    };

    // console.log('[AuthController] Payload for service (SyncProfilePayload):', JSON.stringify(servicePayload, null, 2)); // REMOVE THIS

    return this.authService.syncUserProfile(servicePayload);
  }
}

import { ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { Prisma } from '../../../generated/prisma';
import { HttpAdapterHost } from '@nestjs/core';
export declare class PrismaClientExceptionFilter implements ExceptionFilter {
    private readonly httpAdapterHost;
    constructor(httpAdapterHost: HttpAdapterHost);
    catch(exception: Prisma.PrismaClientKnownRequestError | Prisma.PrismaClientValidationError, host: ArgumentsHost): void;
}

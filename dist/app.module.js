"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const throttler_1 = require("@nestjs/throttler");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const supabase_module_1 = require("./supabase/supabase.module");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const prisma_module_1 = require("./prisma/prisma.module");
const test_module_1 = require("./test/test.module");
const admin_module_1 = require("./admin/admin.module");
const categories_module_1 = require("./categories/categories.module");
const tags_module_1 = require("./tags/tags.module");
const entities_module_1 = require("./entities/entities.module");
const bookmarks_module_1 = require("./bookmarks/bookmarks.module");
const reviews_module_1 = require("./reviews/reviews.module");
const request_logger_middleware_1 = require("./common/middleware/request-logger.middleware");
const admin_entities_module_1 = require("./admin/entities/admin-entities.module");
const entity_types_module_1 = require("./entity-types/entity-types.module");
const features_module_1 = require("./features/features.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer
            .apply(request_logger_middleware_1.RequestLoggerMiddleware)
            .forRoutes({ path: '*', method: common_1.RequestMethod.ALL });
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            throttler_1.ThrottlerModule.forRoot([{
                    ttl: 60000,
                    limit: 20,
                }]),
            supabase_module_1.SupabaseModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            prisma_module_1.PrismaModule,
            test_module_1.TestModule,
            admin_module_1.AdminModule,
            categories_module_1.CategoriesModule,
            tags_module_1.TagsModule,
            entities_module_1.EntitiesModule,
            entity_types_module_1.EntityTypesModule,
            bookmarks_module_1.BookmarksModule,
            reviews_module_1.ReviewsModule,
            admin_entities_module_1.AdminEntitiesModule,
            features_module_1.FeaturesModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            {
                provide: core_1.APP_GUARD,
                useClass: throttler_1.ThrottlerGuard,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map
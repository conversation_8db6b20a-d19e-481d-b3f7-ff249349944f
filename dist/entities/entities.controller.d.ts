import { EntitiesService } from './entities.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { User as UserModelPrisma } from '../../generated/prisma';
import { ListEntitiesDto } from './dto/list-entities.dto';
import { EntityResponseDto } from './dto/entity-response.dto';
import { PaginatedEntityResponseDto } from './dto/paginated-entity-response.dto';
export declare class EntitiesController {
    private readonly entitiesService;
    constructor(entitiesService: EntitiesService);
    private mapUserToMinimalDto;
    private mapEntityTypeToDto;
    private mapCategoryToDto;
    private mapTagToDto;
    private mapFeatureToDto;
    private mapToolDetailsToDto;
    private mapCourseDetailsToDto;
    private mapDatasetDetailsToDto;
    private mapResearchPaperDetailsToDto;
    private mapSoftwareDetailsToDto;
    private mapModelDetailsToDto;
    private mapProjectReferenceDetailsToDto;
    private mapServiceProviderDetailsToDto;
    private mapInvestorDetailsToDto;
    private mapCommunityDetailsToDto;
    private mapEventDetailsToDto;
    private mapJobDetailsToDto;
    private mapGrantDetailsToDto;
    private mapBountyDetailsToDto;
    private mapHardwareDetailsToDto;
    private mapNewsDetailsToDto;
    private mapBookDetailsToDto;
    private mapPodcastDetailsToDto;
    private mapNewsletterDetailsToDto;
    private mapPlatformDetailsToDto;
    private mapAgencyDetailsToDto;
    private mapContentCreatorDetailsToDto;
    private mapEntityToResponseDto;
    create(createEntityDto: CreateEntityDto, req: {
        user: UserModelPrisma;
    }): Promise<EntityResponseDto>;
    findAll(listEntitiesDto: ListEntitiesDto): Promise<PaginatedEntityResponseDto>;
    findOne(id: string): Promise<EntityResponseDto>;
    update(id: string, updateEntityDto: UpdateEntityDto, req: {
        user: UserModelPrisma;
    }): Promise<EntityResponseDto>;
    remove(id: string, req: {
        user: UserModelPrisma;
    }): Promise<void>;
}

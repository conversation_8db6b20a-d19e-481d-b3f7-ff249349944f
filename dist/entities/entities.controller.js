"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntitiesController = void 0;
const common_1 = require("@nestjs/common");
const entities_service_1 = require("./entities.service");
const create_entity_dto_1 = require("./dto/create-entity.dto");
const update_entity_dto_1 = require("./dto/update-entity.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const admin_guard_1 = require("../auth/guards/admin.guard");
const swagger_1 = require("@nestjs/swagger");
const prisma_1 = require("../../generated/prisma");
const list_entities_dto_1 = require("./dto/list-entities.dto");
const entity_response_dto_1 = require("./dto/entity-response.dto");
const paginated_entity_response_dto_1 = require("./dto/paginated-entity-response.dto");
let EntitiesController = class EntitiesController {
    constructor(entitiesService) {
        this.entitiesService = entitiesService;
    }
    mapUserToMinimalDto(user) {
        if (!user)
            return null;
        const minimalDto = {
            id: user.authUserId,
            email: user.email,
            created_at: user.createdAt.toISOString(),
            last_sign_in_at: user.lastLogin ? user.lastLogin.toISOString() : null,
            user_metadata: {
                username: user.username,
                display_name: user.displayName,
                profile_picture_url: user.profilePictureUrl,
                internal_user_id: user.id
            },
        };
        return minimalDto;
    }
    mapEntityTypeToDto(entityType) {
        if (!entityType)
            return null;
        return {
            id: entityType.id,
            name: entityType.name,
            slug: entityType.slug,
            description: entityType.description,
            iconUrl: entityType.iconUrl,
            createdAt: entityType.createdAt,
            updatedAt: entityType.updatedAt,
        };
    }
    mapCategoryToDto(category) {
        if (!category)
            return null;
        return {
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
            iconUrl: category.iconUrl,
            parentCategoryId: category.parentCategoryId,
            createdAt: category.createdAt,
            updatedAt: category.updatedAt,
        };
    }
    mapTagToDto(tag) {
        if (!tag)
            return null;
        return {
            id: tag.id,
            name: tag.name,
            slug: tag.slug,
        };
    }
    mapFeatureToDto(feature) {
        if (!feature)
            return null;
        return {
            id: feature.id,
            name: feature.name,
            slug: feature.slug,
            description: feature.description,
            iconUrl: feature.iconUrl,
            createdAt: feature.createdAt,
            updatedAt: feature.updatedAt,
        };
    }
    mapToolDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            technicalLevel: details.technicalLevel,
            learningCurve: details.learningCurve,
            targetAudience: details.targetAudience,
            hasApi: details.hasApi,
            apiDocumentationUrl: details.apiDocumentationUrl,
            apiSandboxUrl: details.apiSandboxUrl,
            keyFeatures: details.keyFeatures,
            useCases: details.useCases,
            pricingModel: details.pricingModel,
            priceRange: details.priceRange,
            pricingDetails: details.pricingDetails,
            pricingUrl: details.pricingUrl,
            hasFreeTier: details.hasFreeTier,
            platforms: details.platforms,
            integrations: details.integrations,
            supportedLanguages: details.programmingLanguages,
            currentVersion: details.currentVersion,
            lastVersionUpdateDate: details.lastVersionUpdateDate,
            changelogUrl: details.changelogUrl,
        };
    }
    mapCourseDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            instructorName: details.instructorName,
            durationText: details.durationText,
            skillLevel: details.skillLevel,
            prerequisites: details.prerequisites,
            syllabusUrl: details.syllabusUrl,
            enrollmentCount: details.enrollmentCount,
            certificateAvailable: details.certificateAvailable,
        };
    }
    mapDatasetDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            format: details.format,
            sourceUrl: details.sourceUrl,
            license: details.license,
            sizeInBytes: details.sizeInBytes,
            description: details.description,
            accessNotes: details.accessNotes,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapResearchPaperDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            publicationDate: details.publicationDate,
            doi: details.doi,
            authors: details.authors,
            abstract: details.abstract,
            journalOrConference: details.journalOrConference,
            publicationUrl: details.publicationUrl,
            citationCount: details.citationCount,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapSoftwareDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            repositoryUrl: details.repositoryUrl,
            licenseType: details.licenseType,
            programmingLanguages: details.programmingLanguages,
            platformCompatibility: details.platformCompatibility,
            currentVersion: details.currentVersion,
            releaseDate: details.releaseDate,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapModelDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            modelArchitecture: details.modelArchitecture,
            parametersCount: details.parametersCount,
            trainingDataset: details.trainingDataset,
            performanceMetrics: details.performanceMetrics,
            modelUrl: details.modelUrl,
            license: details.license,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapProjectReferenceDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            projectStatus: details.projectStatus,
            sourceCodeUrl: details.sourceCodeUrl,
            liveDemoUrl: details.liveDemoUrl,
            technologies: details.technologies,
            projectGoals: details.projectGoals,
            contributors: details.contributors,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapServiceProviderDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            serviceAreas: details.serviceAreas,
            caseStudiesUrl: details.caseStudiesUrl,
            consultationBookingUrl: details.consultationBookingUrl,
            industrySpecializations: details.industrySpecializations,
            companySizeFocus: details.companySizeFocus,
            hourlyRateRange: details.hourlyRateRange,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapInvestorDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            investmentFocusAreas: details.investmentFocusAreas,
            portfolioUrl: details.portfolioUrl,
            typicalInvestmentSize: details.typicalInvestmentSize,
            investmentStages: details.investmentStages,
            contactEmail: details.contactEmail,
            preferredCommunication: details.preferredCommunication,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapCommunityDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            platform: details.platform,
            memberCount: details.memberCount,
            focusTopics: details.focusTopics,
            rulesUrl: details.rulesUrl,
            inviteUrl: details.inviteUrl,
            mainChannelUrl: details.mainChannelUrl,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapEventDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            eventType: details.eventType,
            startDate: details.startDate,
            endDate: details.endDate,
            location: details.location,
            registrationUrl: details.registrationUrl,
            speakerList: details.speakerList,
            agendaUrl: details.agendaUrl,
            price: details.price,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapJobDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            jobTitle: details.jobTitle,
            companyName: details.companyName,
            locationType: details.locationType,
            salaryRange: details.salaryRange,
            applicationUrl: details.applicationUrl,
            jobDescription: details.jobDescription,
            experienceLevel: details.experienceLevel,
            employmentType: details.employmentType,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapGrantDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            grantingInstitution: details.grantingInstitution,
            eligibilityCriteria: details.eligibilityCriteria,
            applicationDeadline: details.applicationDeadline,
            fundingAmount: details.fundingAmount,
            applicationUrl: details.applicationUrl,
            grantFocusArea: details.grantFocusArea,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapBountyDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            bountyIssuer: details.bountyIssuer,
            rewardAmount: details.rewardAmount,
            requirements: details.requirements,
            submissionDeadline: details.submissionDeadline,
            platformUrl: details.platformUrl,
            difficultyLevel: details.difficultyLevel,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapHardwareDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            hardwareType: details.hardwareType,
            specifications: details.specifications,
            manufacturer: details.manufacturer,
            releaseDate: details.releaseDate,
            priceRange: details.priceRange,
            datasheetUrl: details.datasheetUrl,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapNewsDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            publicationDate: details.publicationDate,
            sourceName: details.sourceName,
            articleUrl: details.articleUrl,
            author: details.author,
            summary: details.summary,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapBookDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            authorNames: details.authorNames,
            isbn: details.isbn,
            publisher: details.publisher,
            publicationYear: details.publicationYear,
            pageCount: details.pageCount,
            summary: details.summary,
            purchaseUrl: details.purchaseUrl,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapPodcastDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            hostNames: details.hostNames,
            averageEpisodeLength: details.averageEpisodeLength,
            mainTopics: details.mainTopics,
            listenUrl: details.listenUrl,
            frequency: details.frequency,
            primaryLanguage: details.primaryLanguage,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapNewsletterDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            frequency: details.frequency,
            mainTopics: details.mainTopics,
            archiveUrl: details.archiveUrl,
            subscribeUrl: details.subscribeUrl,
            authorName: details.authorName,
            subscriberCount: details.subscriberCount,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapPlatformDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            platformType: details.platformType,
            keyServices: details.keyServices,
            documentationUrl: details.documentationUrl,
            pricingModel: details.pricingModel,
            slaUrl: details.slaUrl,
            supportedRegions: details.supportedRegions,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapAgencyDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            servicesOffered: details.servicesOffered,
            industryFocus: details.industryFocus,
            targetClientSize: details.targetClientSize,
            targetAudience: details.targetAudience,
            locationSummary: details.locationSummary,
            portfolioUrl: details.portfolioUrl,
            pricingInfo: details.pricingInfo,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapContentCreatorDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            creatorName: details.creatorName,
            primaryPlatform: details.primaryPlatform,
            focusAreas: details.focusAreas,
            followerCount: details.followerCount,
            exampleContentUrl: details.exampleContentUrl,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapEntityToResponseDto(entity) {
        const responseDto = new entity_response_dto_1.EntityResponseDto();
        responseDto.id = entity.id;
        responseDto.name = entity.name;
        responseDto.websiteUrl = entity.websiteUrl;
        responseDto.entityType = this.mapEntityTypeToDto(entity.entityType);
        responseDto.shortDescription = entity.shortDescription;
        responseDto.description = entity.description;
        responseDto.logoUrl = entity.logoUrl;
        responseDto.documentationUrl = entity.documentationUrl;
        responseDto.contactUrl = entity.contactUrl;
        responseDto.privacyPolicyUrl = entity.privacyPolicyUrl;
        responseDto.foundedYear = entity.foundedYear;
        responseDto.status = entity.status;
        responseDto.socialLinks = entity.socialLinks;
        responseDto.submitter = this.mapUserToMinimalDto(entity.submitter);
        responseDto.legacyId = entity.legacyId;
        responseDto.reviewCount = entity.reviewCount;
        responseDto.avgRating = entity.avgRating;
        responseDto.createdAt = entity.createdAt;
        responseDto.updatedAt = entity.updatedAt;
        responseDto.categories = entity.entityCategories?.map(ec => this.mapCategoryToDto(ec.category)).filter(Boolean) || [];
        responseDto.tags = entity.entityTags?.map(et => this.mapTagToDto(et.tag)).filter(Boolean) || [];
        responseDto.features = entity.entityFeatures?.map(ef => this.mapFeatureToDto(ef.feature)).filter(Boolean) || [];
        switch (entity.entityType?.slug) {
            case 'ai-tool':
                responseDto.details = this.mapToolDetailsToDto(entity.entityDetailsTool);
                break;
            case 'online-course':
                responseDto.details = this.mapCourseDetailsToDto(entity.entityDetailsCourse);
                break;
            case 'dataset':
                responseDto.details = this.mapDatasetDetailsToDto(entity.entityDetailsDataset);
                break;
            case 'research-paper':
                responseDto.details = this.mapResearchPaperDetailsToDto(entity.entityDetailsResearchPaper);
                break;
            case 'software':
                responseDto.details = this.mapSoftwareDetailsToDto(entity.entityDetailsSoftware);
                break;
            case 'ai-model':
            case 'model':
                responseDto.details = this.mapModelDetailsToDto(entity.entityDetailsModel);
                break;
            case 'project-reference':
                responseDto.details = this.mapProjectReferenceDetailsToDto(entity.entityDetailsProjectReference);
                break;
            case 'service-provider':
                responseDto.details = this.mapServiceProviderDetailsToDto(entity.entityDetailsServiceProvider);
                break;
            case 'investor':
                responseDto.details = this.mapInvestorDetailsToDto(entity.entityDetailsInvestor);
                break;
            case 'community':
                responseDto.details = this.mapCommunityDetailsToDto(entity.entityDetailsCommunity);
                break;
            case 'event':
                responseDto.details = this.mapEventDetailsToDto(entity.entityDetailsEvent);
                break;
            case 'job-listing':
            case 'job':
                responseDto.details = this.mapJobDetailsToDto(entity.entityDetailsJob);
                break;
            case 'grant':
                responseDto.details = this.mapGrantDetailsToDto(entity.entityDetailsGrant);
                break;
            case 'bounty':
                responseDto.details = this.mapBountyDetailsToDto(entity.entityDetailsBounty);
                break;
            case 'hardware':
                responseDto.details = this.mapHardwareDetailsToDto(entity.entityDetailsHardware);
                break;
            case 'news':
                responseDto.details = this.mapNewsDetailsToDto(entity.entityDetailsNews);
                break;
            case 'book':
                responseDto.details = this.mapBookDetailsToDto(entity.entityDetailsBook);
                break;
            case 'podcast':
                responseDto.details = this.mapPodcastDetailsToDto(entity.entityDetailsPodcast);
                break;
            case 'newsletter':
                responseDto.details = this.mapNewsletterDetailsToDto(entity.entityDetailsNewsletter);
                break;
            case 'platform':
                responseDto.details = this.mapPlatformDetailsToDto(entity.entityDetailsPlatform);
                break;
            case 'agency':
                responseDto.details = this.mapAgencyDetailsToDto(entity.entityDetailsAgency);
                break;
            case 'content-creator':
                responseDto.details = this.mapContentCreatorDetailsToDto(entity.entityDetailsContentCreator);
                break;
            default:
                responseDto.details = null;
        }
        return responseDto;
    }
    async create(createEntityDto, req) {
        const entity = await this.entitiesService.create(createEntityDto, req.user);
        const fullEntity = await this.entitiesService.findOne(entity.id);
        if (!fullEntity)
            throw new common_1.NotFoundException('Failed to retrieve created entity details.');
        return this.mapEntityToResponseDto(fullEntity);
    }
    async findAll(listEntitiesDto) {
        const result = await this.entitiesService.findAll(listEntitiesDto);
        return {
            data: result.data.map(entity => this.mapEntityToResponseDto(entity)),
            total: result.total,
            page: result.page,
            limit: result.limit,
            totalPages: result.totalPages,
            search: listEntitiesDto.searchTerm,
        };
    }
    async findOne(id) {
        const entity = await this.entitiesService.findOne(id);
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID ${id} not found.`);
        }
        return this.mapEntityToResponseDto(entity);
    }
    async update(id, updateEntityDto, req) {
        const entity = await this.entitiesService.update(id, updateEntityDto, req.user);
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID ${id} not found or update failed.`);
        }
        const fullEntity = await this.entitiesService.findOne(entity.id);
        if (!fullEntity)
            throw new common_1.NotFoundException('Failed to retrieve updated entity details.');
        return this.mapEntityToResponseDto(fullEntity);
    }
    async remove(id, req) {
        await this.entitiesService.remove(id, req.user);
    }
};
exports.EntitiesController = EntitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new entity (submission for review)' }),
    (0, swagger_1.ApiBody)({ type: create_entity_dto_1.CreateEntityDto, description: 'Data to create a new entity' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: 'The entity has been successfully submitted for review.', type: entity_response_dto_1.EntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data. Please check the request body for errors.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.FORBIDDEN, description: 'Forbidden. You do not have permission to perform this action.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CONFLICT, description: 'Conflict. An entity with some unique identifier (e.g., name or URL) might already exist, or a related resource was not found.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_entity_dto_1.CreateEntityDto, Object]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all entities with filtering, pagination, and sorting' }),
    (0, swagger_1.ApiOkResponse)({ description: 'A paginated list of entities matching the criteria.', type: paginated_entity_response_dto_1.PaginatedEntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid query parameters. Please check the filter or pagination values.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, type: String, description: 'Sort field' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, enum: prisma_1.Prisma.SortOrder, description: 'Sort order' }),
    (0, swagger_1.ApiQuery)({ name: 'searchTerm', required: false, type: String, description: 'Search term for name, description' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: prisma_1.EntityStatus, description: 'Filter by status' }),
    (0, swagger_1.ApiQuery)({ name: 'entityTypeId', required: false, type: String, description: 'Filter by entity type ID (UUID)' }),
    (0, swagger_1.ApiQuery)({ name: 'categoryIds', required: false, type: [String], description: 'Filter by category IDs (UUIDs, comma-separated or multiple params)', style: 'form', explode: false }),
    (0, swagger_1.ApiQuery)({ name: 'tagIds', required: false, type: [String], description: 'Filter by tag IDs (UUIDs, comma-separated or multiple params)', style: 'form', explode: false }),
    (0, swagger_1.ApiQuery)({ name: 'submitterId', required: false, type: String, description: 'Filter by submitter user ID (UUID)' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [list_entities_dto_1.ListEntitiesDto]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a single entity by its ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', required: true, description: 'UUID of the entity to retrieve', type: String, format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'The entity was found and returned.', type: entity_response_dto_1.EntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid UUID format for entity ID.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update an existing entity. Submitter can update PENDING/ACTIVE/REJECTED/INACTIVE entities (ACTIVE changes to PENDING). Admin can update any.' }),
    (0, swagger_1.ApiParam)({ name: 'id', required: true, description: 'UUID of the entity to update', type: String, format: 'uuid' }),
    (0, swagger_1.ApiBody)({ type: update_entity_dto_1.UpdateEntityDto, description: 'Data to update the entity' }),
    (0, swagger_1.ApiOkResponse)({ description: 'The entity has been successfully updated.', type: entity_response_dto_1.EntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data or invalid UUID format.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.FORBIDDEN, description: 'Forbidden. You do not have permission to update this entity or perform this status transition.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found, or a related resource (like a category/tag ID) is invalid.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CONFLICT, description: 'Conflict. An update would violate a unique constraint (e.g., name or URL).' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_entity_dto_1.UpdateEntityDto, Object]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Archive an entity by ID (Admin only). This performs a soft delete.' }),
    (0, swagger_1.ApiParam)({ name: 'id', required: true, description: 'UUID of the entity to archive', type: String, format: 'uuid' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NO_CONTENT, description: 'The entity has been successfully archived.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid UUID format for entity ID.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.FORBIDDEN, description: 'Forbidden. Only administrators can archive entities.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "remove", null);
exports.EntitiesController = EntitiesController = __decorate([
    (0, swagger_1.ApiTags)('Entities'),
    (0, common_1.Controller)('entities'),
    __metadata("design:paramtypes", [entities_service_1.EntitiesService])
], EntitiesController);
//# sourceMappingURL=entities.controller.js.map
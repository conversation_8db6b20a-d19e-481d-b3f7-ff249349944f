module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: 'src',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  testPathIgnorePatterns: ['/node_modules/', '/generated/'],
  collectCoverageFrom: ['**/*.ts'],
  coverageDirectory: '../coverage',
  moduleNameMapper: {
    '^@generated-prisma(|/.*)$': '<rootDir>/../generated/prisma$1',
  },
}; 
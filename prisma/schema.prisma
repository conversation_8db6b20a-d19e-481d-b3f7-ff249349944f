// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  output          = "../generated/prisma"
  previewFeatures = ["multiSchema", "fullTextSearchPostgres"]
  binaryTargets   = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public"]
}

// Enums based on database inspection
enum UserRole {
  USER
  ADMIN
  MODERATOR
  // Add other roles discovered if any

  @@schema("public")
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
  DELETED // Added for soft deletion

  @@schema("public")
}

enum TechnicalLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
  // Add other levels discovered if any

  @@schema("public")
}

enum EntityStatus {
  PENDING
  ACTIVE
  REJECTED
  NEEDS_REVISION
  INACTIVE
  ARCHIVED // Added for soft deletion

  @@schema("public")
}

enum AffiliateStatus {
  NONE
  APPLIED
  APPROVED
  REJECTED
  // Add other statuses discovered if any

  @@schema("public")
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
  // Add other statuses discovered if any

  @@schema("public")
}

enum LearningCurve {
  LOW
  MEDIUM
  HIGH
  // Add other curves discovered if any

  @@schema("public")
}

enum PricingModel {
  FREE
  FREEMIUM
  SUBSCRIPTION // Replaces PAID, or is distinct if PAID was meant to be kept and mapped
  PAY_PER_USE // Replaces USAGE_BASED
  ONE_TIME_PURCHASE // Replaces ONE_TIME
  CONTACT_SALES // New
  OPEN_SOURCE // New
  // OTHER // Intentionally removed as per plan

  @@schema("public")
}

enum PriceRange {
  FREE
  LOW
  MEDIUM
  HIGH
  ENTERPRISE
  // Add other ranges discovered if any

  @@schema("public")
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
  // Add other levels discovered if any

  @@schema("public")
}

enum ActionType {
  VIEW_ENTITY
  CLICK_ENTITY_LINK
  SAVE_ENTITY
  UNSAVE_ENTITY
  SUBMIT_REVIEW
  VOTE_REVIEW
  FOLLOW_TAG
  UNFOLLOW_TAG
  FOLLOW_CATEGORY
  UNFOLLOW_CATEGORY
  SEARCH
  LOGIN
  LOGOUT
  SIGNUP
  UPDATE_PROFILE
  GRANT_BADGE
  REVOKE_BADGE
  // Add other actions discovered if any

  @@schema("public")
}

enum BadgeScope {
  USER
  ENTITY
  // Add other scopes discovered if any

  @@schema("public")
}

// Models based on database inspection

model User {
  id                       String                    @id @default(uuid()) @db.Uuid
  authUserId               String                    @unique @map("auth_user_id") @db.Uuid
  username                 String?                   @unique
  displayName              String?                   @map("display_name")
  email                    String                    @unique
  role                     UserRole                  @default(USER)
  status                   UserStatus                @default(ACTIVE)
  technicalLevel           TechnicalLevel?           @map("technical_level")
  profilePictureUrl        String?                   @map("profile_picture_url")
  bio                      String?
  socialLinks              Json?                     @map("social_links") @db.JsonB
  createdAt                DateTime                  @default(now()) @map("created_at")
  updatedAt                DateTime                  @updatedAt @map("updated_at")
  lastLogin                DateTime?                 @map("last_login")
  reviews                  Review[]
  reviewsModerated         Review[]                  @relation("ModeratorReviews")
  userSavedEntities        UserSavedEntity[]         @relation("UserSavedEntities")
  userFollowedTags         UserFollowedTag[]         @relation("UserFollowedTags")
  userFollowedCategories   UserFollowedCategory[]    @relation("UserFollowedCategories")
  submittedEntities        Entity[]                  @relation("Submitter")
  userActivityLogs         UserActivityLog[]         @relation("UserLogs")
  userActivityLogTargets   UserActivityLog[]         @relation("TargetUserLogs")
  userNotificationSettings UserNotificationSettings? @relation("UserNotificationSettings")
  userBadges               UserBadge[]               @relation("UserBadges")
  badgesGranted            UserBadge[]               @relation("UserBadgesGranted")
  entityBadgesGranted      EntityBadge[]             @relation("EntityBadgesGranted")
  reviewVotes              ReviewVote[]              @relation("UserReviewVotes")

  @@map("users")
  @@schema("public")
}

model EntityType {
  id           String   @id @default(uuid()) @db.Uuid
  name         String   @unique
  description  String?
  slug         String   @unique
  iconUrl      String?  @map("icon_url")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  entities     Entity[]

  @@map("entity_types")
  @@schema("public")
}

model Entity {
  id               String       @id @default(uuid()) @db.Uuid
  name             String
  websiteUrl       String?      @map("website_url")
  entityTypeId     String       @map("entity_type_id") @db.Uuid
  shortDescription String?      @map("short_description")
  description      String?
  logoUrl          String?      @map("logo_url")
  documentationUrl String?      @map("documentation_url")
  contactUrl       String?      @map("contact_url")
  privacyPolicyUrl String?      @map("privacy_policy_url")
  foundedYear      Int?         @map("founded_year")
  status           EntityStatus @default(PENDING)
  socialLinks      Json?        @map("social_links") @db.JsonB
  submitterId      String       @map("submitter_id") @db.Uuid
  legacyId         String?      @map("legacy_id")
  reviewCount      Int          @default(0) @map("review_count")
  avgRating        Float        @default(0) @map("avg_rating")
  createdAt        DateTime     @default(now()) @map("created_at")
  updatedAt        DateTime     @updatedAt @map("updated_at")

  // SEO
  metaTitle                 String? @map("meta_title")
  metaDescription           String? @map("meta_description")

  // Scraped Review Sentiment
  scrapedReviewSentimentLabel String? @map("scraped_review_sentiment_label")
  scrapedReviewSentimentScore Float?  @map("scraped_review_sentiment_score")
  scrapedReviewCount          Int?    @default(0) @map("scraped_review_count")

  // Company Info
  employeeCountRange        String? @map("employee_count_range")
  fundingStage              String? @map("funding_stage")
  locationSummary           String? @map("location_summary")

  // Core fields
  refLink                   String? @map("ref_link")
  affiliateStatus           AffiliateStatus? @default(NONE) // Ensuring type and default

  // Vector Embedding
  vectorEmbedding           Unsupported("vector(1536)")? @map("vector_embedding")

  // Relations
  entityType        EntityType        @relation(fields: [entityTypeId], references: [id], map: "fk_entity_entity_type_id")
  submitter         User              @relation("Submitter", fields: [submitterId], references: [id], map: "fk_entity_submitter_id")
  entityCategories  EntityCategory[]  @relation("EntityToCategories")
  entityTags        EntityTag[]       @relation("EntityToTags")
  reviews           Review[]          @relation("EntityReviews")
  userSavedEntities UserSavedEntity[] @relation("SavedEntities")
  userActivityLogs  UserActivityLog[] @relation("EntityLogs")
  entityBadges      EntityBadge[]     @relation("EntityBadges")
  entityFeatures    EntityFeature[]   @relation("EntityToFeatures")

  // Entity Detail Relations (One-to-One)
  entityDetailsTool             EntityDetailsTool?             @relation("EntityToDetailsTool")
  entityDetailsCourse           EntityDetailsCourse?           @relation("EntityToDetailsCourse")
  entityDetailsDataset          EntityDetailsDataset?          @relation("EntityToDetailsDataset")
  entityDetailsResearchPaper    EntityDetailsResearchPaper?    @relation("EntityToDetailsResearchPaper")
  entityDetailsSoftware         EntityDetailsSoftware?         @relation("EntityToDetailsSoftware")
  entityDetailsModel            EntityDetailsModel?            @relation("EntityToDetailsModel")
  entityDetailsProjectReference EntityDetailsProjectReference? @relation("EntityToDetailsProjectReference")
  entityDetailsServiceProvider  EntityDetailsServiceProvider?  @relation("EntityToDetailsServiceProvider")
  entityDetailsInvestor         EntityDetailsInvestor?         @relation("EntityToDetailsInvestor")
  entityDetailsCommunity        EntityDetailsCommunity?        @relation("EntityToDetailsCommunity")
  entityDetailsEvent            EntityDetailsEvent?            @relation("EntityToDetailsEvent")
  entityDetailsJob              EntityDetailsJob?              @relation("EntityToDetailsJob")
  entityDetailsGrant            EntityDetailsGrant?            @relation("EntityToDetailsGrant")
  entityDetailsBounty           EntityDetailsBounty?           @relation("EntityToDetailsBounty")
  entityDetailsHardware         EntityDetailsHardware?         @relation("EntityToDetailsHardware")
  entityDetailsNews             EntityDetailsNews?             @relation("EntityToDetailsNews")
  entityDetailsBook             EntityDetailsBook?             @relation("EntityToDetailsBook")
  entityDetailsPodcast          EntityDetailsPodcast?          @relation("EntityToDetailsPodcast")
  entityDetailsNewsletter       EntityDetailsNewsletter?       @relation("EntityToDetailsNewsletter")
  entityDetailsPlatform         EntityDetailsPlatform?         @relation("EntityToDetailsPlatform")
  entityDetailsAgency           EntityDetailsAgency?           @relation("EntityToDetailsAgency")
  entityDetailsContentCreator   EntityDetailsContentCreator?   @relation("EntityToDetailsContentCreator")

  // For PostgreSQL full-text search on name, description, and shortDescription:
  // @@fulltext([name, description, shortDescription], map: "entity_full_text_search_idx") // Commented out due to P1012

  @@index([entityTypeId], map: "idx_entity_entity_type_id")
  @@index([submitterId])
  @@index([status])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([name])
  @@map("entities")
  @@schema("public")
  @@unique([name])
}

model Category {
  id                     String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                   String                 @unique
  slug                   String                 @unique
  description            String?
  iconUrl                String?                @map("icon_url")
  parentCategoryId       String?                @map("parent_category_id") @db.Uuid
  createdAt              DateTime               @default(now()) @map("created_at")
  updatedAt              DateTime               @updatedAt @map("updated_at")
  entities               EntityCategory[]       @relation("CategoryToEntities")
  parentCategory         Category?              @relation("SubCategories", fields: [parentCategoryId], references: [id], onDelete: SetNull, map: "fk_category_parent_category_id")
  subCategories          Category[]             @relation("SubCategories")
  userFollowedCategories UserFollowedCategory[] @relation("FollowedCategories")
  userActivityLogs       UserActivityLog[]      @relation("CategoryLogs")

  @@index([parentCategoryId])
  @@map("categories")
  @@schema("public")
}

model Tag {
  id               String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name             String            @unique
  description      String?
  slug             String            @unique
  iconUrl          String?           @map("icon_url")
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  entities         EntityTag[]       @relation("TagToEntities")
  userFollowedTags UserFollowedTag[] @relation("FollowedTags")
  userActivityLogs UserActivityLog[] @relation("TagLogs")

  @@map("tags")
  @@schema("public")
}

model EntityTag {
  entityId String @map("entity_id") @db.Uuid
  tagId    String @map("tag_id") @db.Uuid
  entity   Entity @relation("EntityToTags", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_tag_entity_id")
  tag      Tag    @relation("TagToEntities", fields: [tagId], references: [id], onDelete: Cascade, map: "fk_entity_tag_tag_id")

  @@id([entityId, tagId])
  @@map("entity_tags")
  @@schema("public")
}

model EntityCategory {
  entityId   String   @map("entity_id") @db.Uuid
  categoryId String   @map("category_id") @db.Uuid
  assignedAt DateTime @default(now()) @map("assigned_at")

  entity   Entity   @relation("EntityToCategories", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_category_entity_id")
  category Category @relation("CategoryToEntities", fields: [categoryId], references: [id], onDelete: Cascade, map: "fk_entity_category_category_id")

  @@id([entityId, categoryId], map: "entity_categories_pkey")
  @@index([categoryId], map: "idx_entity_category_category_id")
  @@index([entityId], map: "idx_entity_category_entity_id")
  @@map("entity_categories")
  @@schema("public")
}

model Review {
  id               String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  entityId         String            @map("entity_id") @db.Uuid
  userId           String            @map("user_id") @db.Uuid
  rating           Int
  title            String?
  reviewText       String?           @map("review_text")
  status           ReviewStatus      @default(PENDING)
  helpfulnessScore Int               @default(0) @map("helpfulness_score")
  moderatorUserId  String?           @map("moderator_user_id") @db.Uuid
  moderatedAt      DateTime?         @map("moderated_at")
  moderationNotes  String?           @map("moderation_notes")
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  entity           Entity            @relation("EntityReviews", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_review_entity_id")
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade, map: "fk_review_user_id")
  moderator        User?             @relation("ModeratorReviews", fields: [moderatorUserId], references: [id], onDelete: SetNull, map: "fk_review_moderator_id")
  reviewVotes      ReviewVote[]      @relation("ReviewVotes")
  userActivityLogs UserActivityLog[] @relation("ReviewLogs")

  @@unique([userId, entityId])
  @@index([entityId])
  @@index([userId])
  @@index([moderatorUserId])
  @@map("reviews")
  @@schema("public")
}

model ReviewVote {
  reviewId  String   @map("review_id") @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  vote      Int      @db.SmallInt
  createdAt DateTime @default(now()) @map("created_at")
  review    Review   @relation("ReviewVotes", fields: [reviewId], references: [id], onDelete: Cascade)
  user      User     @relation("UserReviewVotes", fields: [userId], references: [id], onDelete: Cascade)

  @@id([reviewId, userId])
  @@map("review_votes")
  @@schema("public")
}

model EntityDetailsTool {
  entityId         String        @id @map("entity_id") @db.Uuid
  programmingLanguages Json?         @map("programming_languages") @db.JsonB // Assuming this might store string[]
  frameworks       Json?         @db.JsonB // Assuming this might store string[]
  libraries        Json?         @db.JsonB // Assuming this might store string[]
  integrations     Json?         @map("integrations") @db.JsonB // Will store string[]
  keyFeatures      Json?         @map("key_features") @db.JsonB // Assuming this might store string[]
  useCases         Json?         @map("use_cases") @db.JsonB // Will store string[]
  targetAudience   Json?         @map("target_audience") @db.JsonB // Assuming this might store string[]
  learningCurve    LearningCurve? @map("learning_curve")
  deploymentOptions Json?         @map("deployment_options") @db.JsonB // Assuming this might store string[]
  supportedOs      Json?         @map("supported_os") @db.JsonB // Assuming this might store string[]
  mobileSupport    Boolean?      @map("mobile_support")
  apiAccess        Boolean?      @map("api_access")
  customizationLevel String?       @map("customization_level")
  trialAvailable   Boolean?      @map("trial_available")
  demoAvailable    Boolean?      @map("demo_available")
  openSource       Boolean?      @map("open_source")
  supportChannels  Json?         @map("support_channels") @db.JsonB // Assuming this might store string[]

  // New fields based on plan
  hasFreeTier      Boolean       @default(false) @map("has_free_tier")
  pricingModel     PricingModel? @map("pricing_model")
  priceRange       PriceRange?   @map("price_range")
  pricingDetails   String?       @map("pricing_details")
  pricingUrl       String?       @map("pricing_url")
  supportEmail     String?       @map("support_email")
  hasLiveChat      Boolean?      @default(false) @map("has_live_chat")
  communityUrl     String?       @map("community_url")

  // Relation
  entity Entity @relation("EntityToDetailsTool", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_tool_entity_id")

  @@map("entity_details_tool")
  @@schema("public")
}

model EntityDetailsAgency {
  entityId         String  @id @map("entity_id") @db.Uuid
  servicesOffered  Json?   @map("services_offered") @db.JsonB
  industryFocus    Json?   @map("industry_focus") @db.JsonB
  targetClientSize Json?   @map("target_client_size") @db.JsonB
  targetAudience   Json?   @map("target_audience") @db.JsonB
  locationSummary  String? @map("location_summary")
  portfolioUrl     String? @map("portfolio_url")
  pricingInfo      String? @map("pricing_info")
  entity           Entity  @relation("EntityToDetailsAgency", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_agency_entity_id")

  @@map("entity_details_agency")
  @@schema("public")
}

model EntityDetailsContentCreator {
  entityId          String  @id @map("entity_id") @db.Uuid
  creatorName       String? @map("creator_name")
  primaryPlatform   String? @map("primary_platform")
  focusAreas        Json?   @map("focus_areas") @db.JsonB
  followerCount     Int?    @default(0) @map("follower_count")
  exampleContentUrl String? @map("example_content_url")
  entity            Entity  @relation("EntityToDetailsContentCreator", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_content_creator_entity_id")

  @@map("entity_details_content_creator")
  @@schema("public")
}

model EntityDetailsCommunity {
  entityId       String  @id @map("entity_id") @db.Uuid
  platform       String?
  memberCount    Int?    @default(0) @map("member_count")
  focusTopics    Json?   @map("focus_topics") @db.JsonB
  rulesUrl       String? @map("rules_url")
  inviteUrl      String? @map("invite_url")
  mainChannelUrl String? @map("main_channel_url")
  entity         Entity  @relation("EntityToDetailsCommunity", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_community_entity_id")

  @@map("entity_details_community")
  @@schema("public")
}

model EntityDetailsNewsletter {
  entityId        String  @id @map("entity_id") @db.Uuid
  frequency       String?
  mainTopics      Json?   @map("main_topics") @db.JsonB
  archiveUrl      String? @map("archive_url")
  subscribeUrl    String? @map("subscribe_url")
  authorName      String? @map("author_name")
  subscriberCount Int?    @default(0) @map("subscriber_count")
  entity          Entity  @relation("EntityToDetailsNewsletter", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_newsletter_entity_id")

  @@map("entity_details_newsletter")
  @@schema("public")
}

model EntityDetailsCourse {
  entityId             String      @id @map("entity_id") @db.Uuid
  instructorName       String?     @map("instructor_name")
  durationText         String?     @map("duration_text")
  skillLevel           SkillLevel? @map("skill_level")
  prerequisites        String?
  syllabusUrl          String?     @map("syllabus_url")
  enrollmentCount      Int?        @default(0) @map("enrollment_count")
  certificateAvailable Boolean?    @default(false) @map("certificate_available")
  entity               Entity      @relation("EntityToDetailsCourse", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_course_entity_id")

  @@map("entity_details_course")
  @@schema("public")
}

model UserSavedEntity {
  userId    String   @map("user_id") @db.Uuid
  entityId  String   @map("entity_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation("UserSavedEntities", fields: [userId], references: [id], onDelete: Cascade, map: "fk_user_saved_entity_user_id")
  entity    Entity   @relation("SavedEntities", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_user_saved_entity_entity_id")

  @@id([userId, entityId])
  @@map("user_saved_entities")
  @@schema("public")
}

model UserFollowedTag {
  userId    String   @map("user_id") @db.Uuid
  tagId     String   @map("tag_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation("UserFollowedTags", fields: [userId], references: [id], onDelete: Cascade)
  tag       Tag      @relation("FollowedTags", fields: [tagId], references: [id], onDelete: Cascade)

  @@id([userId, tagId])
  @@map("user_followed_tags")
  @@schema("public")
}

model UserFollowedCategory {
  userId     String   @map("user_id") @db.Uuid
  categoryId String   @map("category_id") @db.Uuid
  createdAt  DateTime @default(now()) @map("created_at")
  user       User     @relation("UserFollowedCategories", fields: [userId], references: [id], onDelete: Cascade)
  category   Category @relation("FollowedCategories", fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([userId, categoryId])
  @@map("user_followed_categories")
  @@schema("public")
}

model UserActivityLog {
  id           String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId       String     @map("user_id") @db.Uuid
  actionType   ActionType @map("action_type")
  entityId     String?    @map("entity_id") @db.Uuid
  categoryId   String?    @map("category_id") @db.Uuid
  tagId        String?    @map("tag_id") @db.Uuid
  reviewId     String?    @map("review_id") @db.Uuid
  targetUserId String?    @map("target_user_id") @db.Uuid
  timestamp    DateTime   @default(now())
  details      Json?      @db.JsonB
  user         User       @relation("UserLogs", fields: [userId], references: [id], onDelete: Cascade, map: "fk_user_activity_log_user_id")
  entity       Entity?    @relation("EntityLogs", fields: [entityId], references: [id], onDelete: SetNull, map: "fk_user_activity_log_entity_id")
  category     Category?  @relation("CategoryLogs", fields: [categoryId], references: [id], onDelete: SetNull, map: "fk_user_activity_log_category_id")
  tag          Tag?       @relation("TagLogs", fields: [tagId], references: [id], onDelete: SetNull, map: "fk_user_activity_log_tag_id")
  review       Review?    @relation("ReviewLogs", fields: [reviewId], references: [id], onDelete: SetNull, map: "fk_user_activity_log_review_id")
  targetUser   User?      @relation("TargetUserLogs", fields: [targetUserId], references: [id], onDelete: SetNull, map: "fk_user_activity_log_target_user_id")

  @@index([userId])
  @@index([actionType])
  @@index([entityId])
  @@index([categoryId])
  @@index([tagId])
  @@index([reviewId])
  @@index([targetUserId])
  @@map("user_activity_logs")
  @@schema("public")
}

model UserNotificationSettings {
  userId                           String   @id @map("user_id") @db.Uuid
  emailNewsletter                  Boolean  @default(true) @map("email_newsletter")
  emailNewEntityInFollowedCategory Boolean  @default(true) @map("email_new_entity_in_followed_category")
  emailNewEntityInFollowedTag      Boolean  @default(false) @map("email_new_entity_in_followed_tag")
  emailNewReviewOnSavedEntity      Boolean  @default(true) @map("email_new_review_on_saved_entity")
  emailUpdatesOnSavedEntity        Boolean  @default(false) @map("email_updates_on_saved_entity")
  createdAt                        DateTime @default(now()) @map("created_at")
  updatedAt                        DateTime @updatedAt @map("updated_at")
  user                             User     @relation("UserNotificationSettings", fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_notification_settings")
  @@schema("public")
}

model BadgeType {
  id              String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name            String        @unique
  slug            String        @unique
  description     String
  iconUrl         String        @map("icon_url")
  criteriaDetails String?       @map("criteria_details")
  scope           BadgeScope
  isAutoGranted   Boolean       @default(false) @map("is_auto_granted")
  isManualGranted Boolean       @default(true) @map("is_manual_granted")
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  userBadges      UserBadge[]   @relation("BadgeTypeUser")
  entityBadges    EntityBadge[] @relation("BadgeTypeEntity")

  @@map("badge_types")
  @@schema("public")
}

model UserBadge {
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId          String    @map("user_id") @db.Uuid
  badgeTypeId     String    @map("badge_type_id") @db.Uuid
  grantedAt       DateTime  @default(now()) @map("granted_at")
  grantedByUserId String?   @map("granted_by_user_id") @db.Uuid
  notes           String?
  user            User      @relation("UserBadges", fields: [userId], references: [id], onDelete: Cascade)
  badgeType       BadgeType @relation("BadgeTypeUser", fields: [badgeTypeId], references: [id], onDelete: Cascade)
  grantedByUser   User?     @relation("UserBadgesGranted", fields: [grantedByUserId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([badgeTypeId])
  @@index([grantedByUserId])
  @@map("user_badges")
  @@schema("public")
}

model EntityBadge {
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  entityId        String    @map("entity_id") @db.Uuid
  badgeTypeId     String    @map("badge_type_id") @db.Uuid
  grantedAt       DateTime  @default(now()) @map("granted_at")
  grantedByUserId String?   @map("granted_by_user_id") @db.Uuid
  notes           String?
  entity          Entity    @relation("EntityBadges", fields: [entityId], references: [id], onDelete: Cascade)
  badgeType       BadgeType @relation("BadgeTypeEntity", fields: [badgeTypeId], references: [id], onDelete: Cascade)
  grantedByUser   User?     @relation("EntityBadgesGranted", fields: [grantedByUserId], references: [id], onDelete: SetNull)

  @@index([entityId])
  @@index([badgeTypeId])
  @@index([grantedByUserId])
  @@map("entity_badges")
  @@schema("public")
}

// NOTE: We haven't explicitly defined a model for auth.users here.
// Prisma can reference it via the FK in entities.verifiedBy, but we won't manage auth.users directly via this Prisma schema.
// Interactions with Supabase Auth should happen via the Supabase client library (e.g., in AuthService).

// Updated definition for EntityDetailsDataset
// You will need to add ALL other missing EntityDetails models mentioned in the errors,
// such as EntityDetailsResearchPaper, EntityDetailsSoftware, etc.
// Ensure each has a similar one-to-one relation back to the Entity model.
model EntityDetailsDataset {
  entityId    String  @id @map("entity_id") @db.Uuid
  format      String? // e.g., CSV, JSON, Parquet
  sourceUrl   String? @map("source_url")
  license     String? // e.g., MIT, CC BY 4.0
  sizeInBytes BigInt? @map("size_in_bytes")
  description String?
  accessNotes String? @map("access_notes") // Notes on how to access or use the dataset

  entity    Entity   @relation("EntityToDetailsDataset", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_dataset_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_dataset")
  @@schema("public")
}

// Example for EntityDetailsResearchPaper
model EntityDetailsResearchPaper {
  entityId            String    @id @map("entity_id") @db.Uuid
  publicationDate     DateTime? @map("publication_date") @db.Date
  doi                 String?   @unique
  authors             Json? // Store as array of strings like ["Author One", "Author Two"]
  abstract            String?
  journalOrConference String?   @map("journal_or_conference")
  publicationUrl      String?   @map("publication_url")
  citationCount       Int?      @default(0) @map("citation_count")

  entity    Entity   @relation("EntityToDetailsResearchPaper", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_research_paper_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_research_paper")
  @@schema("public")
}

model EntityDetailsSoftware {
  entityId              String    @id @map("entity_id") @db.Uuid
  repositoryUrl         String?   @map("repository_url")
  licenseType           String?   @map("license_type")
  programmingLanguages  Json?     @map("programming_languages") // Array of strings, e.g., ["Python", "JavaScript"]
  platformCompatibility Json?     @map("platform_compatibility") // Array of strings, e.g., ["Windows", "Linux", "Web"]
  currentVersion        String?   @map("current_version")
  releaseDate           DateTime? @map("release_date") @db.Date

  // Shared fields from EntityDetailsTool
  hasFreeTier      Boolean       @default(false) @map("has_free_tier")
  useCases         Json?         @map("use_cases") // Will store string[]
  pricingModel     PricingModel? @map("pricing_model")
  priceRange       PriceRange?   @map("price_range")
  pricingDetails   String?       @map("pricing_details")
  pricingUrl       String?       @map("pricing_url")
  integrations     Json?         @map("integrations") // Will store string[]
  supportEmail     String?       @map("support_email")
  hasLiveChat      Boolean?      @default(false) @map("has_live_chat")
  communityUrl     String?       @map("community_url")

  entity    Entity   @relation("EntityToDetailsSoftware", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_software_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_software")
  @@schema("public")
}

model EntityDetailsModel {
  entityId           String  @id @map("entity_id") @db.Uuid
  modelArchitecture  String? @map("model_architecture")
  parametersCount    BigInt? @map("parameters_count")
  trainingDataset    String? @map("training_dataset") // Could be a description or link
  performanceMetrics Json? // e.g., { "accuracy": 0.95, "f1_score": 0.92 }
  modelUrl           String? @map("model_url") // Link to download or access the model
  license            String?

  entity    Entity   @relation("EntityToDetailsModel", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_model_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_model")
  @@schema("public")
}

model EntityDetailsProjectReference {
  entityId      String  @id @map("entity_id") @db.Uuid
  projectStatus String? @map("project_status") // e.g., active, completed, archived, proof-of-concept
  sourceCodeUrl String? @map("source_code_url")
  liveDemoUrl   String? @map("live_demo_url")
  technologies  Json? // Array of strings, e.g., ["React", "Node.js", "PostgreSQL"]
  projectGoals  String? @map("project_goals")
  contributors  Json? // Array of contributor names or objects

  entity    Entity   @relation("EntityToDetailsProjectReference", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_project_reference_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_project_reference")
  @@schema("public")
}

model EntityDetailsServiceProvider {
  entityId                String  @id @map("entity_id") @db.Uuid
  serviceAreas            Json? // e.g., ["AI Development", "Data Science Consulting", "MLOps"]
  caseStudiesUrl          String? @map("case_studies_url")
  consultationBookingUrl  String? @map("consultation_booking_url")
  industrySpecializations Json? // Array of strings, e.g., ["Healthcare", "Finance"]
  companySizeFocus        String? @map("company_size_focus") // e.g., Startups, SMEs, Enterprise
  hourlyRateRange         String? @map("hourly_rate_range") // e.g., "$100-$200"

  entity    Entity   @relation("EntityToDetailsServiceProvider", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_service_provider_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_service_provider")
  @@schema("public")
}

model EntityDetailsInvestor {
  entityId               String  @id @map("entity_id") @db.Uuid
  investmentFocusAreas   Json? // e.g., ["Seed Stage AI", "Healthcare Tech", "SaaS"]
  portfolioUrl           String? @map("portfolio_url")
  typicalInvestmentSize  String? @map("typical_investment_size") // e.g., "$100k - $1M"
  investmentStages       Json? // Array of strings, e.g., ["Pre-seed", "Seed", "Series A"]
  contactEmail           String? @map("contact_email")
  preferredCommunication String? @map("preferred_communication")

  entity    Entity   @relation("EntityToDetailsInvestor", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_investor_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_investor")
  @@schema("public")
}

model EntityDetailsEvent {
  entityId        String    @id @map("entity_id") @db.Uuid
  eventType       String? // e.g., Conference, Webinar, Workshop, Meetup
  startDate       DateTime? @map("start_date")
  endDate         DateTime? @map("end_date")
  location        String? // Can be physical address or "Online"
  registrationUrl String?   @map("registration_url")
  speakerList     Json? // Array of speaker names or objects
  agendaUrl       String?   @map("agenda_url")
  price           String? // e.g., "Free", "$99", "Contact for enterprise"

  entity    Entity   @relation("EntityToDetailsEvent", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_event_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_event")
  @@schema("public")
}

model EntityDetailsJob {
  entityId        String  @id @map("entity_id") @db.Uuid
  jobTitle        String? @map("job_title")
  companyName     String? @map("company_name")
  locationType    String? @map("location_type") // e.g., Remote, On-site, Hybrid
  salaryRange     String? @map("salary_range") // e.g., "$80k - $120k", "Competitive"
  applicationUrl  String? @map("application_url")
  jobDescription  String? @map("job_description")
  experienceLevel String? @map("experience_level") // e.g., Entry, Mid, Senior
  employmentType  String? @map("employment_type") // e.g., Full-time, Part-time, Contract

  entity    Entity   @relation("EntityToDetailsJob", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_job_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_job")
  @@schema("public")
}

model EntityDetailsGrant {
  entityId            String    @id @map("entity_id") @db.Uuid
  grantingInstitution String?   @map("granting_institution")
  eligibilityCriteria String?   @map("eligibility_criteria")
  applicationDeadline DateTime? @map("application_deadline") @db.Date
  fundingAmount       String?   @map("funding_amount") // e.g., "$10,000 - $50,000"
  applicationUrl      String?   @map("application_url")
  grantFocusArea      String?   @map("grant_focus_area") // e.g., "AI Ethics Research", "Open Source Development"

  entity    Entity   @relation("EntityToDetailsGrant", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_grant_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_grant")
  @@schema("public")
}

model EntityDetailsBounty {
  entityId           String    @id @map("entity_id") @db.Uuid
  bountyIssuer       String?   @map("bounty_issuer")
  rewardAmount       String?   @map("reward_amount") // e.g., "1000 USD", "0.5 ETH"
  requirements       String?
  submissionDeadline DateTime? @map("submission_deadline") @db.Date
  platformUrl        String?   @map("platform_url") // e.g., Link to Gitcoin, HackerOne, or custom platform
  difficultyLevel    String?   @map("difficulty_level") // e.g., Easy, Medium, Hard

  entity    Entity   @relation("EntityToDetailsBounty", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_bounty_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_bounty")
  @@schema("public")
}

model EntityDetailsHardware {
  entityId       String    @id @map("entity_id") @db.Uuid
  hardwareType   String?   @map("hardware_type") // e.g., GPU, FPGA, ASIC, TPU, AI Accelerator
  specifications Json? // e.g., { "memory": "24GB GDDR6X", "cuda_cores": 10496, "tflops": 35.6 }
  manufacturer   String?
  releaseDate    DateTime? @map("release_date") @db.Date
  priceRange     String?   @map("price_range") // e.g., "$500 - $1000"
  datasheetUrl   String?   @map("datasheet_url")

  entity    Entity   @relation("EntityToDetailsHardware", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_hardware_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_hardware")
  @@schema("public")
}

model EntityDetailsNews {
  entityId        String    @id @map("entity_id") @db.Uuid
  publicationDate DateTime? @map("publication_date") @db.Date
  sourceName      String?   @map("source_name")
  articleUrl      String    @unique @map("article_url")
  author          String?
  summary         String? // Brief summary of the news article

  entity    Entity   @relation("EntityToDetailsNews", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_news_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_news")
  @@schema("public")
}

model EntityDetailsBook {
  entityId        String  @id @map("entity_id") @db.Uuid
  authorNames     Json? // Array of strings, e.g., ["Author A", "Author B"]
  isbn            String? @unique
  publisher       String?
  publicationYear Int?    @map("publication_year")
  pageCount       Int?    @map("page_count")
  summary         String?
  purchaseUrl     String? @map("purchase_url")

  entity    Entity   @relation("EntityToDetailsBook", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_book_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_book")
  @@schema("public")
}

model EntityDetailsPodcast {
  entityId             String  @id @map("entity_id") @db.Uuid
  hostNames            Json? // Array of strings
  averageEpisodeLength String? @map("average_episode_length") // e.g., "45 minutes"
  mainTopics           Json? // Array of strings, e.g., ["AI Ethics", "ML Research"]
  listenUrl            String? @map("listen_url") // Link to Spotify, Apple Podcasts, etc.
  frequency            String? // e.g., "Weekly", "Bi-weekly"
  primaryLanguage      String? @default("English") @map("primary_language")

  entity    Entity   @relation("EntityToDetailsPodcast", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_podcast_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_podcast")
  @@schema("public")
}

model EntityDetailsPlatform {
  entityId         String  @id @map("entity_id") @db.Uuid
  platformType     String? @map("platform_type") // e.g., PaaS, SaaS, IaaS, MLOps Platform, Data Platform
  keyServices      Json?   @map("key_services") // Array of strings, e.g., ["Model Training", "Data Annotation", "Deployment"] // Added map
  documentationUrl String? @map("documentation_url")
  pricingModel     PricingModel? @map("pricing_model") // Changed from String? to PricingModel? (Final Change)
  slaUrl           String? @map("sla_url")
  supportedRegions Json?   @map("supported_regions") // Array of strings, e.g., ["us-east-1", "eu-west-2"]

  // Shared fields from EntityDetailsTool
  hasFreeTier      Boolean?      @default(false) @map("has_free_tier") // Optional as not in direct SQL
  useCases         Json?         @map("use_cases") // Will store string[]
  priceRange       PriceRange?   @map("price_range")
  pricingDetails   String?       @map("pricing_details")
  pricingUrl       String?       @map("pricing_url")
  integrations     Json?         @map("integrations") // Will store string[]
  supportEmail     String?       @map("support_email")
  hasLiveChat      Boolean?      @default(false) @map("has_live_chat")
  communityUrl     String?       @map("community_url")

  entity    Entity   @relation("EntityToDetailsPlatform", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_details_platform_entity_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entity_details_platform")
  @@schema("public")
}

model EntityFeature {
  entityId   String   @map("entity_id") @db.Uuid
  featureId  String   @map("feature_id") @db.Uuid
  assignedAt DateTime @default(now()) @map("assigned_at")
  // assignedBy could be a String? relation to User id if needed in future
  // assignedById String? @map("assigned_by_id") @db.Uuid 
  // assignedBy User? @relation("FeaturesAssignedByUser", fields: [assignedById], references: [id])

  entity  Entity  @relation("EntityToFeatures", fields: [entityId], references: [id], onDelete: Cascade, map: "fk_entity_feature_entity_id")
  feature Feature @relation("FeatureToEntities", fields: [featureId], references: [id], onDelete: Cascade, map: "fk_entity_feature_feature_id")

  @@id([entityId, featureId], map: "pk_entity_feature")
  @@index([featureId], map: "idx_entity_feature_feature_id")
  @@index([entityId], map: "idx_entity_feature_entity_id")
  @@map("entity_features")
  @@schema("public")
}

// Define Feature model here
model Feature {
  id             String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name           String          @unique
  slug           String          @unique
  description    String?
  iconUrl        String?         @map("icon_url")
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")
  entityFeatures EntityFeature[] @relation("FeatureToEntities")

  @@map("features")
  @@schema("public")
}

// ADD OTHER MISSING EntityDetails... MODELS HERE, following the pattern above.
// For example:
// model EntityDetailsSoftware {
